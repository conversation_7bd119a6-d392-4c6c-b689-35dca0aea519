#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re
import sys

def create_font_header(input_file, output_file, font_name, font_description):
    try:
        # Ler o arquivo .c
        with open(input_file, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()

        # Extrair os dados do array
        pattern = r'unsigned char rawData\[\d+\] = \{([^}]+)\};'
        match = re.search(pattern, content, re.DOTALL)

        if match:
            array_data = match.group(1).strip()

            # Criar o conteúdo do header
            header_content = f'''#pragma once

//---------------------------------------------------------------------
// 		🔤	{font_description}
//---------------------------------------------------------------------

static const unsigned char {font_name}[] = {{
''' + array_data + f'''
}};

//---------------------------------------------------------------------
// 		📏	Font Size Information
//---------------------------------------------------------------------

static const int {font_name}_size = sizeof({font_name});'''

            # Salvar o arquivo
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(header_content)

            print(f'Arquivo {output_file} criado com sucesso!')
            array_size = len([x for x in array_data.split(',') if x.strip()])
            print(f'Tamanho do array: {array_size} bytes')
        else:
            print('Erro: Não foi possível extrair os dados do array')

    except Exception as e:
        print(f'Erro: {e}')

def create_montserrat_regular():
    create_font_header(
        'fonts/Montserrat-Regular.c',
        'montserrat_font.h',
        'montserrat_regular',
        'Montserrat Regular Font Data'
    )

def create_montserrat_bold():
    create_font_header(
        'fonts/Montserrat-Bold.c',
        'montserrat_bold_font.h',
        'montserrat_bold',
        'Montserrat Bold Font Data'
    )

if __name__ == '__main__':
    if len(sys.argv) > 1:
        if sys.argv[1] == 'bold':
            create_montserrat_bold()
        elif sys.argv[1] == 'regular':
            create_montserrat_regular()
        else:
            print('Uso: python create_font_header.py [regular|bold]')
    else:
        create_montserrat_regular()
