//---------------------------------------------------------------------
// 		📝	Exemplo de Uso da Fonte Montserrat
//---------------------------------------------------------------------

#include "montserrat_font.h"
#include "ImGui/imgui.h"

//---------------------------------------------------------------------
// 		🔤	Exemplo 1: Usand<PERSON> Montserrat em Elementos Específicos
//---------------------------------------------------------------------

void ExemploUsoFonteEspecifica()
{
    ImGuiIO& io = ImGui::GetIO();
    
    // Verificar se temos fontes carregadas
    if (io.Fonts->Fonts.Size > 0)
    {
        // Usar Montserrat (primeira fonte adicionada) para um texto específico
        ImGui::PushFont(io.Fonts->Fonts[0]); // Montserrat Regular
        ImGui::Text("Este texto está em Montserrat Regular!");
        ImGui::PopFont();
        
        // Texto normal com fonte padrão
        ImGui::Text("Este texto usa a fonte padrão.");
        
        // Usar Montserrat para um botão
        ImGui::PushFont(io.Fonts->Fonts[0]);
        if (ImGui::Button("Botão com Montserrat"))
        {
            // Ação do botão
        }
        ImGui::PopFont();
    }
}

//---------------------------------------------------------------------
// 		🔤	Exemplo 2: Usando Diferentes Tamanhos da Mesma Fonte
//---------------------------------------------------------------------

void ExemploMultiplosTamanhos()
{
    ImGuiIO& io = ImGui::GetIO();
    
    // Se você carregar múltiplas versões da fonte com tamanhos diferentes:
    // io.Fonts->Fonts[0] = Montserrat 16px
    // io.Fonts->Fonts[1] = Fonte padrão 18px
    
    if (io.Fonts->Fonts.Size > 1)
    {
        // Título com fonte maior
        ImGui::PushFont(io.Fonts->Fonts[1]);
        ImGui::Text("TÍTULO GRANDE");
        ImGui::PopFont();
        
        // Texto normal com Montserrat
        ImGui::PushFont(io.Fonts->Fonts[0]);
        ImGui::Text("Texto normal em Montserrat");
        ImGui::PopFont();
    }
}

//---------------------------------------------------------------------
// 		🔤	Exemplo 3: Aplicando Fonte a uma Janela Inteira
//---------------------------------------------------------------------

void ExemploJanelaComMontserrat()
{
    ImGuiIO& io = ImGui::GetIO();
    
    if (io.Fonts->Fonts.Size > 0)
    {
        // Aplicar Montserrat para toda a janela
        ImGui::PushFont(io.Fonts->Fonts[0]);
        
        if (ImGui::Begin("Janela com Montserrat"))
        {
            ImGui::Text("Todo o texto desta janela está em Montserrat!");
            
            if (ImGui::Button("Botão em Montserrat"))
            {
                // Ação do botão
            }
            
            ImGui::Checkbox("Checkbox em Montserrat", nullptr);
            
            static float valor = 0.5f;
            ImGui::SliderFloat("Slider em Montserrat", &valor, 0.0f, 1.0f);
        }
        ImGui::End();
        
        // Restaurar fonte padrão
        ImGui::PopFont();
    }
}

//---------------------------------------------------------------------
// 		🔤	Exemplo 4: Verificação de Segurança
//---------------------------------------------------------------------

void ExemploVerificacaoSeguranca()
{
    ImGuiIO& io = ImGui::GetIO();
    
    // Sempre verificar se a fonte existe antes de usar
    if (io.Fonts->Fonts.Size > 0 && io.Fonts->Fonts[0] != nullptr)
    {
        ImGui::PushFont(io.Fonts->Fonts[0]);
        ImGui::Text("Fonte Montserrat carregada com sucesso!");
        ImGui::PopFont();
    }
    else
    {
        ImGui::Text("Fonte Montserrat não está disponível - usando fonte padrão");
    }
}

//---------------------------------------------------------------------
// 		🔤	Exemplo 5: Função Auxiliar para Facilitar o Uso
//---------------------------------------------------------------------

// Função auxiliar para usar Montserrat facilmente
void TextoMontserrat(const char* texto)
{
    ImGuiIO& io = ImGui::GetIO();
    
    if (io.Fonts->Fonts.Size > 0 && io.Fonts->Fonts[0] != nullptr)
    {
        ImGui::PushFont(io.Fonts->Fonts[0]);
        ImGui::Text("%s", texto);
        ImGui::PopFont();
    }
    else
    {
        ImGui::Text("%s", texto); // Fallback para fonte padrão
    }
}

// Função auxiliar para botões com Montserrat
bool BotaoMontserrat(const char* label)
{
    ImGuiIO& io = ImGui::GetIO();
    bool resultado = false;
    
    if (io.Fonts->Fonts.Size > 0 && io.Fonts->Fonts[0] != nullptr)
    {
        ImGui::PushFont(io.Fonts->Fonts[0]);
        resultado = ImGui::Button(label);
        ImGui::PopFont();
    }
    else
    {
        resultado = ImGui::Button(label); // Fallback para fonte padrão
    }
    
    return resultado;
}

//---------------------------------------------------------------------
// 		🔤	Exemplo de Uso das Funções Auxiliares
//---------------------------------------------------------------------

void ExemploFuncoesAuxiliares()
{
    TextoMontserrat("Este texto usa a função auxiliar!");
    
    if (BotaoMontserrat("Clique aqui"))
    {
        // Ação do botão
    }
}
