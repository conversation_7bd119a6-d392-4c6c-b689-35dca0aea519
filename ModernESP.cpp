#include "Modules.h"
#include "ModernESP.h"

namespace ModernESP {

    //---------------------------------------------------------------------
    // 		⚙️	Configurações do Sistema ESP
    //---------------------------------------------------------------------
    namespace Config {
        // ESP Principal - Usar variáveis existentes do projeto
        bool& enabled = mods::esp;
        bool& teamCheck = mods::TeamCheck;
        float maxDistance = 500.0f;
        bool& visibilityCheck = mods::VisCheck;

        // Boxes
        bool showBoxes = true;
        ESPBoxType boxType = ESP_BOX_2D;
        bool boxOutline = true;
        float boxThickness = 1.0f;
        ImU32 visibleColor = IM_COL32(0, 255, 0, 255);
        ImU32 nonVisibleColor = IM_COL32(255, 0, 0, 255);
        ImU32 boxOutlineColor = IM_COL32(0, 0, 0, 255);

        // Barras de Saúde
        bool showHealthBar = true;
        ESPFeaturePosition healthBarPosition = POSITION_LEFT;
        ImU32 healthHighColor = IM_COL32(0, 255, 0, 250);
        ImU32 healthMidColor = IM_COL32(255, 255, 0, 250);
        ImU32 healthLowColor = IM_COL32(255, 0, 0, 250);
        ImU32 healthBarOutlineColor = IM_COL32(0, 0, 0, 255);
        ImU32 barBackgroundColor = IM_COL32(0, 0, 0, 200);

        // Barras de Ultimate
        bool showUltimateBar = true;
        ESPFeaturePosition ultimateBarPosition = POSITION_RIGHT;
        ImU32 ultimateBarColor = IM_COL32(255, 255, 0, 250);
        ImU32 ultimateBarOutlineColor = IM_COL32(0, 0, 0, 255);

        // Texto e Informações
        bool showPlayerNames = true;
        bool showHeroNames = false;
        bool showDistance = true;
        bool showHealthText = false;
        bool showUltimateText = false;
        ESPFeaturePosition namePosition = POSITION_TOP;
        ESPFeaturePosition distancePosition = POSITION_BOTTOM;
        float textScale = 1.0f;
        float baseFontSize = 12.0f;
        ImU32 nameTextColor = IM_COL32(255, 255, 255, 255);
        ImU32 nameTextOutlineColor = IM_COL32(0, 0, 0, 255);
        ImU32 nameBackgroundColor = IM_COL32(0, 0, 0, 150);
        ImU32 distanceTextColor = IM_COL32(255, 255, 255, 255);
        ImU32 distanceTextOutlineColor = IM_COL32(0, 0, 0, 255);
        ImU32 healthTextColor = IM_COL32(255, 255, 255, 255);
        ImU32 healthTextOutlineColor = IM_COL32(0, 0, 0, 255);
        ImU32 ultimateTextColor = IM_COL32(255, 255, 255, 255);
        ImU32 ultimateTextOutlineColor = IM_COL32(0, 0, 0, 255);

        // Skeleton ESP
        bool showSkeleton = false;
        ImU32 skeletonColor = IM_COL32(255, 255, 255, 255);
        float skeletonThickness = 1.0f;

        // Tracers
        bool showTracers = false;
        TracerStartPosition tracerStartPos = TRACER_CENTER;
        ImU32 tracerColor = IM_COL32(255, 255, 0, 255);
        float tracerThickness = 1.0f;

        // Crosshair
        bool showCrosshair = false;
        CrosshairType crosshairType = CROSSHAIR_DOT;
        ImU32 crosshairColor = IM_COL32(255, 255, 255, 255);
        float crosshairSize = 5.0f;
        float crosshairThickness = 1.0f;

        // Snap Lines
        bool showSnapLines = false;
        ImU32 snapLineColor = IM_COL32(255, 0, 0, 255);
        float snapLineThickness = 1.0f;

        // Glow
        bool showGlow = false;
        bool glowThroughWalls = true;
        bool glowIgnoreTeammates = true;

        // Configurações Avançadas
        bool dynamicColors = true;
        bool fadeWithDistance = false;
        float fadeStartDistance = 300.0f;
        float fadeEndDistance = 500.0f;
        bool showOnlyInFOV = false;
        float fovLimit = 90.0f;
    }

    //---------------------------------------------------------------------
    // 		🎨	Mapeamento de Heróis
    //---------------------------------------------------------------------
    const std::unordered_map<int, std::string> heroIDToName = {
        {1011, "Hulk"}, {1014, "Punisher"}, {1015, "Storm"}, {1016, "Loki"}, {1017, "Human Torch"},
        {1018, "Doctor Strange"}, {1020, "Mantis"}, {1021, "Hawkeye"}, {1022, "Captain America"},
        {1023, "Rocket Raccoon"}, {1024, "Hela"}, {1025, "Dagger"}, {1026, "Black Panther"},
        {1027, "Groot"}, {1029, "Magik"}, {1030, "Moon Knight"}, {1031, "Luna Snow"},
        {1032, "Squirrel Girl"}, {1033, "Black Widow"}, {1034, "Iron Man"}, {1035, "Venom"},
        {1036, "Spider Man"}, {1037, "Magneto"}, {1038, "Scarlet Witch"}, {1039, "Thor"},
        {1040, "Mister Fantastic"}, {1041, "Winter Soldier"}, {1042, "Peni Parker"},
        {1043, "Star Lord"}, {1045, "Namor"}, {1046, "Adam Warlock"}, {1047, "Jeff"},
        {1048, "Psylocke"}, {1049, "Wolverine"}, {1050, "Invisible Woman"}, {1051, "The Thing"},
        {1052, "Iron Fist"}, {4016, "Galacta Bot"}, {4018, "Galacta Bot Plus"}
    };

    //---------------------------------------------------------------------
    // 		🦴	Conexões de Ossos para Skeleton ESP
    //---------------------------------------------------------------------
    const std::vector<std::pair<std::wstring, std::wstring>> boneConnections = {
        { L"spine_01", L"pelvis" },
        { L"neck_01", L"spine_01" },
        { L"head", L"neck_01" },
        { L"upperarm_l", L"spine_01" },
        { L"lowerarm_l", L"upperarm_l" },
        { L"hand_l", L"lowerarm_l" },
        { L"upperarm_r", L"spine_01" },
        { L"lowerarm_r", L"upperarm_r" },
        { L"hand_r", L"lowerarm_r" },
        { L"thigh_l", L"pelvis" },
        { L"calf_l", L"thigh_l" },
        { L"foot_l", L"calf_l" },
        { L"thigh_r", L"pelvis" },
        { L"calf_r", L"thigh_r" },
        { L"foot_r", L"calf_r" }
    };

    //---------------------------------------------------------------------
    // 		📊	Estatísticas
    //---------------------------------------------------------------------
    namespace Stats {
        int playersRendered = 0;
        int playersVisible = 0;
        int playersInRange = 0;
        float lastUpdateTime = 0.0f;

        void Reset() {
            playersRendered = 0;
            playersVisible = 0;
            playersInRange = 0;
        }

        void Update() {
            lastUpdateTime = SDK::UGameplayStatics::GetTimeSeconds(Variables::World);
        }
    }

    //---------------------------------------------------------------------
    // 		🔧	Funções Principais
    //---------------------------------------------------------------------
    void Initialize() {
        LoadDefaultConfig();
        Stats::Reset();
    }

    //---------------------------------------------------------------------
    // 		🎯	Função Principal de Renderização ESP
    //---------------------------------------------------------------------
    void RenderESP(SDK::TArray<SDK::AActor*>& ActorList, 
                   SDK::APlayerController* PlayerController, 
                   ImDrawList* BackgroundList, 
                   ImDrawList* ForegroundList,
                   SDK::FName HeadSocketName) {
        
        if (!Config::enabled || !IsValidPtr(BackgroundList) || !IsValidPtr(PlayerController)) {
            return;
        }

        Stats::Reset();

        // Obter classe para filtrar jogadores
        SDK::UClass* ClassToFind = SDK::AMarvelBaseCharacter::StaticClass();
        if (!IsValidObjectPtr(ClassToFind)) {
            return;
        }

        // Renderizar crosshair se ativado
        if (Config::showCrosshair && IsValidPtr(ForegroundList)) {
            RenderCrosshair(ForegroundList);
        }

        // Loop através de todos os atores
        SafeArrayLoop("ModernESP_Players", ActorList, [&](SDK::AActor* actor, int i) -> bool {
            // Verificar se é um jogador
            bool isPlayer = Modules::CheckIfPlayer(actor, ClassToFind);
            bool isSummon = Modules::CheckIfSummonedTarget(actor);

            if (!isPlayer && !isSummon) {
                return true; // Continuar loop
            }

            // Se for summon, renderizar ESP básico
            if (isSummon && !isPlayer) {
                SDK::FVector WorldLocation = actor->K2_GetActorLocation();
                SDK::FVector2D ScreenLocation;
                if (PlayerController->ProjectWorldLocationToScreen(WorldLocation, &ScreenLocation, true)) {
                    BackgroundList->AddCircle(ImVec2(ScreenLocation.X, ScreenLocation.Y), 8.0f, 
                                            IM_COL32(255, 165, 0, 255), 12, 2.0f);
                    AddTextWithOutline(BackgroundList, ImVec2(ScreenLocation.X + 15, ScreenLocation.Y - 10), 
                                     IM_COL32(255, 165, 0, 255), IM_COL32(0, 0, 0, 255), "SUMMON");
                }
                return true;
            }

            // Cast para AMarvelBaseCharacter
            SDK::AMarvelBaseCharacter* Player = static_cast<SDK::AMarvelBaseCharacter*>(actor);
            if (!IsValidObjectPtr(Player)) {
                return true;
            }

            // Verificar se deve renderizar este jogador
            if (!ShouldRenderPlayer(Player, PlayerController)) {
                return true;
            }

            Stats::playersInRange++;

            // Coletar dados do jogador
            ESPRenderData data = CollectPlayerData(Player, PlayerController, HeadSocketName);
            if (!data.isOnScreen) {
                return true;
            }

            Stats::playersRendered++;
            if (data.isVisible) {
                Stats::playersVisible++;
            }

            // Renderizar ESP do jogador
            RenderPlayerESP(data, BackgroundList, PlayerController);

            // Aplicar glow se ativado
            if (Config::showGlow) {
                ApplyGlow(Player, data.isEnemy);
            }

            return true;
        });

        Stats::Update();
    }

    //---------------------------------------------------------------------
    // 		🎨	Renderizar ESP do Jogador
    //---------------------------------------------------------------------
    void RenderPlayerESP(const ESPRenderData& data, ImDrawList* drawList, SDK::APlayerController* controller) {
        if (!IsValidPtr(drawList)) return;

        // Renderizar box
        if (Config::showBoxes) {
            RenderBox(data, drawList);
        }

        // Renderizar barra de saúde
        if (Config::showHealthBar) {
            RenderHealthBar(data, drawList);
        }

        // Renderizar barra de ultimate
        if (Config::showUltimateBar) {
            RenderUltimateBar(data, drawList);
        }

        // Renderizar informações do jogador
        RenderPlayerInfo(data, drawList);

        // Renderizar tracers
        if (Config::showTracers) {
            RenderTracers(data, drawList);
        }
    }

    //---------------------------------------------------------------------
    // 		📦	Renderizar Box
    //---------------------------------------------------------------------
    void RenderBox(const ESPRenderData& data, ImDrawList* drawList) {
        if (!IsValidPtr(drawList)) return;

        ImU32 color = GetPlayerColor(data);
        
        if (Config::boxType == ESP_BOX_2D) {
            // Box 2D simples
            if (Config::boxOutline) {
                drawList->AddRect(ImVec2(data.screenMin.X, data.screenMin.Y), 
                                ImVec2(data.screenMax.X, data.screenMax.Y), 
                                Config::boxOutlineColor, 0.0f, ImDrawFlags_None, 
                                Config::boxThickness + 2.0f);
            }
            drawList->AddRect(ImVec2(data.screenMin.X, data.screenMin.Y), 
                            ImVec2(data.screenMax.X, data.screenMax.Y), 
                            color, 0.0f, ImDrawFlags_None, Config::boxThickness);
        }
        // TODO: Implementar ESP_BOX_3D e ESP_BOX_CORNERED
    }

    //---------------------------------------------------------------------
    // 		💚	Renderizar Barra de Saúde
    //---------------------------------------------------------------------
    void RenderHealthBar(const ESPRenderData& data, ImDrawList* drawList) {
        if (!IsValidPtr(drawList) || data.maxHealth <= 0.0f) return;

        float healthPercent = data.health / data.maxHealth;
        ImVec2 barStart, barEnd;

        // Determinar posição da barra baseada na configuração
        switch (Config::healthBarPosition) {
            case POSITION_LEFT:
                barStart = ImVec2(data.screenMin.X - 10.0f, data.screenMin.Y);
                barEnd = ImVec2(data.screenMin.X - 5.0f, data.screenMax.Y);
                break;
            case POSITION_RIGHT:
                barStart = ImVec2(data.screenMax.X + 5.0f, data.screenMin.Y);
                barEnd = ImVec2(data.screenMax.X + 10.0f, data.screenMax.Y);
                break;
            case POSITION_TOP:
                barStart = ImVec2(data.screenMin.X, data.screenMin.Y - 10.0f);
                barEnd = ImVec2(data.screenMax.X, data.screenMin.Y - 5.0f);
                break;
            case POSITION_BOTTOM:
                barStart = ImVec2(data.screenMin.X, data.screenMax.Y + 5.0f);
                barEnd = ImVec2(data.screenMax.X, data.screenMax.Y + 10.0f);
                break;
        }

        ImU32 healthColor = GetHealthColor(healthPercent);

        // Renderizar barra vertical ou horizontal
        if (Config::healthBarPosition == POSITION_LEFT || Config::healthBarPosition == POSITION_RIGHT) {
            float barHeight = barEnd.y - barStart.y;
            ImVec2 fillTopLeft(barStart.x, barEnd.y - (healthPercent * barHeight));
            ImVec2 fillBottomRight(barEnd.x, barEnd.y);

            drawList->AddRectFilled(barStart, barEnd, Config::barBackgroundColor);
            drawList->AddRectFilled(fillTopLeft, fillBottomRight, healthColor);
            drawList->AddRect(barStart, barEnd, Config::healthBarOutlineColor);
        } else {
            float barWidth = barEnd.x - barStart.x;
            ImVec2 fillTopLeft(barStart.x, barStart.y);
            ImVec2 fillBottomRight(barStart.x + (healthPercent * barWidth), barEnd.y);

            drawList->AddRectFilled(barStart, barEnd, Config::barBackgroundColor);
            drawList->AddRectFilled(fillTopLeft, fillBottomRight, healthColor);
            drawList->AddRect(barStart, barEnd, Config::healthBarOutlineColor);
        }
    }

    //---------------------------------------------------------------------
    // 		⚡	Renderizar Barra de Ultimate
    //---------------------------------------------------------------------
    void RenderUltimateBar(const ESPRenderData& data, ImDrawList* drawList) {
        if (!IsValidPtr(drawList)) return;

        float ultPercent = data.ultPercentage / 100.0f;
        ImVec2 barStart, barEnd;

        // Determinar posição da barra baseada na configuração
        switch (Config::ultimateBarPosition) {
            case POSITION_LEFT:
                barStart = ImVec2(data.screenMin.X - 20.0f, data.screenMin.Y);
                barEnd = ImVec2(data.screenMin.X - 15.0f, data.screenMax.Y);
                break;
            case POSITION_RIGHT:
                barStart = ImVec2(data.screenMax.X + 15.0f, data.screenMin.Y);
                barEnd = ImVec2(data.screenMax.X + 20.0f, data.screenMax.Y);
                break;
            case POSITION_TOP:
                barStart = ImVec2(data.screenMin.X, data.screenMin.Y - 20.0f);
                barEnd = ImVec2(data.screenMax.X, data.screenMin.Y - 15.0f);
                break;
            case POSITION_BOTTOM:
                barStart = ImVec2(data.screenMin.X, data.screenMax.Y + 15.0f);
                barEnd = ImVec2(data.screenMax.X, data.screenMax.Y + 20.0f);
                break;
        }

        // Renderizar barra vertical ou horizontal
        if (Config::ultimateBarPosition == POSITION_LEFT || Config::ultimateBarPosition == POSITION_RIGHT) {
            float barHeight = barEnd.y - barStart.y;
            ImVec2 fillTopLeft(barStart.x, barEnd.y - (ultPercent * barHeight));
            ImVec2 fillBottomRight(barEnd.x, barEnd.y);

            drawList->AddRectFilled(barStart, barEnd, Config::barBackgroundColor);
            drawList->AddRectFilled(fillTopLeft, fillBottomRight, Config::ultimateBarColor);
            drawList->AddRect(barStart, barEnd, Config::ultimateBarOutlineColor);
        } else {
            float barWidth = barEnd.x - barStart.x;
            ImVec2 fillTopLeft(barStart.x, barStart.y);
            ImVec2 fillBottomRight(barStart.x + (ultPercent * barWidth), barEnd.y);

            drawList->AddRectFilled(barStart, barEnd, Config::barBackgroundColor);
            drawList->AddRectFilled(fillTopLeft, fillBottomRight, Config::ultimateBarColor);
            drawList->AddRect(barStart, barEnd, Config::ultimateBarOutlineColor);
        }
    }

    //---------------------------------------------------------------------
    // 		📝	Renderizar Informações do Jogador
    //---------------------------------------------------------------------
    void RenderPlayerInfo(const ESPRenderData& data, ImDrawList* drawList) {
        if (!IsValidPtr(drawList)) return;

        float fontSize = Config::baseFontSize * Config::textScale;

        // Renderizar nome do jogador
        if (Config::showPlayerNames && !data.playerName.empty()) {
            ImVec2 textPos = GetTextPosition(data, Config::namePosition, data.playerName.c_str(), fontSize);

            // Background do texto
            ImVec2 textSize = ImGui::GetFont()->CalcTextSizeA(fontSize, FLT_MAX, 0.0f, data.playerName.c_str());
            ImVec2 bgStart = { textPos.x - 5.0f, textPos.y - 2.0f };
            ImVec2 bgEnd = { textPos.x + textSize.x + 5.0f, textPos.y + textSize.y + 2.0f };
            drawList->AddRectFilled(bgStart, bgEnd, Config::nameBackgroundColor);

            AddTextWithOutline(drawList, textPos, Config::nameTextColor, Config::nameTextOutlineColor,
                             data.playerName.c_str(), fontSize);
        }

        // Renderizar distância
        if (Config::showDistance) {
            std::string distanceStr = std::to_string(static_cast<int>(data.distance)) + "m";
            ImVec2 textPos = GetTextPosition(data, Config::distancePosition, distanceStr.c_str(), fontSize);
            AddTextWithOutline(drawList, textPos, Config::distanceTextColor, Config::distanceTextOutlineColor,
                             distanceStr.c_str(), fontSize);
        }

        // Renderizar texto de saúde
        if (Config::showHealthText) {
            std::string healthStr = std::to_string(static_cast<int>(data.health));
            ImVec2 textPos = GetHealthTextPosition(data, fontSize);
            AddTextWithOutline(drawList, textPos, Config::healthTextColor, Config::healthTextOutlineColor,
                             healthStr.c_str(), fontSize);
        }

        // Renderizar texto de ultimate
        if (Config::showUltimateText) {
            std::string ultStr = std::to_string(static_cast<int>(data.ultPercentage)) + "%";
            ImVec2 textPos = GetUltimateTextPosition(data, fontSize);
            AddTextWithOutline(drawList, textPos, Config::ultimateTextColor, Config::ultimateTextOutlineColor,
                             ultStr.c_str(), fontSize);
        }
    }

    //---------------------------------------------------------------------
    // 		🎨	Funções Utilitárias
    //---------------------------------------------------------------------
    ImVec2 GetTextPosition(const ESPRenderData& data, ESPFeaturePosition position, const char* text, float fontSize) {
        ImVec2 textSize = ImGui::GetFont()->CalcTextSizeA(fontSize, FLT_MAX, 0.0f, text);
        ImVec2 textPos;

        switch (position) {
            case POSITION_TOP:
                textPos = ImVec2((data.screenMin.X + data.screenMax.X) / 2.0f - textSize.x / 2,
                               data.screenMin.Y - textSize.y - 5);
                break;
            case POSITION_BOTTOM:
                textPos = ImVec2((data.screenMin.X + data.screenMax.X) / 2.0f - textSize.x / 2,
                               data.screenMax.Y + 5);
                break;
            case POSITION_LEFT:
                textPos = ImVec2(data.screenMin.X - textSize.x - 5,
                               (data.screenMin.Y + data.screenMax.Y) / 2.0f - textSize.y / 2);
                break;
            case POSITION_RIGHT:
                textPos = ImVec2(data.screenMax.X + 5,
                               (data.screenMin.Y + data.screenMax.Y) / 2.0f - textSize.y / 2);
                break;
        }
        return textPos;
    }

    ImVec2 GetHealthTextPosition(const ESPRenderData& data, float fontSize) {
        ImVec2 textPos;
        switch (Config::healthBarPosition) {
            case POSITION_TOP:
                textPos = ImVec2((data.screenMin.X + data.screenMax.X) / 2.0f, data.screenMin.Y - 15);
                break;
            case POSITION_BOTTOM:
                textPos = ImVec2((data.screenMin.X + data.screenMax.X) / 2.0f, data.screenMax.Y + 15);
                break;
            case POSITION_LEFT:
                textPos = ImVec2(data.screenMin.X - 15, (data.screenMin.Y + data.screenMax.Y) / 2.0f);
                break;
            case POSITION_RIGHT:
                textPos = ImVec2(data.screenMax.X + 15, (data.screenMin.Y + data.screenMax.Y) / 2.0f);
                break;
        }
        return textPos;
    }

    ImVec2 GetUltimateTextPosition(const ESPRenderData& data, float fontSize) {
        ImVec2 textPos;
        switch (Config::ultimateBarPosition) {
            case POSITION_TOP:
                textPos = ImVec2((data.screenMin.X + data.screenMax.X) / 2.0f, data.screenMin.Y - 25);
                break;
            case POSITION_BOTTOM:
                textPos = ImVec2((data.screenMin.X + data.screenMax.X) / 2.0f, data.screenMax.Y + 25);
                break;
            case POSITION_LEFT:
                textPos = ImVec2(data.screenMin.X - 25, (data.screenMin.Y + data.screenMax.Y) / 2.0f);
                break;
            case POSITION_RIGHT:
                textPos = ImVec2(data.screenMax.X + 25, (data.screenMin.Y + data.screenMax.Y) / 2.0f);
                break;
        }
        return textPos;
    }

    ImU32 GetPlayerColor(const ESPRenderData& data) {
        if (Config::dynamicColors) {
            if (data.isVisible) {
                return Config::visibleColor;
            } else {
                return Config::nonVisibleColor;
            }
        }
        return data.isEnemy ? Config::nonVisibleColor : Config::visibleColor;
    }

    ImU32 GetHealthColor(float healthPercent) {
        if (healthPercent >= 0.7f) {
            return Config::healthHighColor;
        } else if (healthPercent >= 0.3f) {
            return Config::healthMidColor;
        } else {
            return Config::healthLowColor;
        }
    }

    void AddTextWithOutline(ImDrawList* drawList, ImVec2 pos, ImU32 textColor, ImU32 outlineColor,
                           const char* text, float fontSize) {
        if (!IsValidPtr(drawList)) return;

        if (fontSize <= 0.0f) {
            fontSize = Config::baseFontSize * Config::textScale;
        }

        ImFont* font = ImGui::GetFont();

        // Renderizar outline
        drawList->AddText(font, fontSize, ImVec2(pos.x - 1, pos.y), outlineColor, text);
        drawList->AddText(font, fontSize, ImVec2(pos.x + 1, pos.y), outlineColor, text);
        drawList->AddText(font, fontSize, ImVec2(pos.x, pos.y - 1), outlineColor, text);
        drawList->AddText(font, fontSize, ImVec2(pos.x, pos.y + 1), outlineColor, text);

        // Renderizar texto principal
        drawList->AddText(font, fontSize, pos, textColor, text);
    }

    //---------------------------------------------------------------------
    // 		📊	Coletar Dados do Jogador
    //---------------------------------------------------------------------
    ESPRenderData CollectPlayerData(SDK::AMarvelBaseCharacter* player, SDK::APlayerController* controller, SDK::FName headSocketName) {
        ESPRenderData data = {};

        if (!IsValidObjectPtr(player) || !IsValidPtr(controller)) {
            return data;
        }

        SDK::USkeletalMeshComponent* mesh = player->GetMesh();
        if (!IsValidObjectPtr(mesh)) {
            return data;
        }

        // Obter posições 3D
        SDK::FVector headPos3D = mesh->GetSocketLocation(headSocketName);
        SDK::FVector footPos3D = mesh->GetSocketLocation(SDK::UKismetStringLibrary::Conv_StringToName(SDK::FString(L"Root")));

        // Projetar para tela
        if (!controller->ProjectWorldLocationToScreen(headPos3D, &data.headPos, true) ||
            !controller->ProjectWorldLocationToScreen(footPos3D, &data.footPos, true)) {
            return data;
        }

        data.isOnScreen = true;

        // Calcular bounding box
        data.screenMin = SDK::FVector2D(
            std::min(data.headPos.X, data.footPos.X) - 50.0f,
            std::min(data.headPos.Y, data.footPos.Y) - 10.0f
        );
        data.screenMax = SDK::FVector2D(
            std::max(data.headPos.X, data.footPos.X) + 50.0f,
            std::max(data.headPos.Y, data.footPos.Y) + 10.0f
        );

        // Obter informações do jogador
        data.health = player->GetCurrentHealth();
        data.maxHealth = player->GetMaxHealth();
        data.distance = SDK::UKismetMathLibrary::Vector_Distance(Variables::CameraLocation, player->K2_GetActorLocation()) / 100.0f;

        // Nome do jogador
        try {
            data.playerName = player->GetPlayerName(true).ToString();
        } catch (...) {
            data.playerName = "Unknown";
        }

        // Informações do herói
        data.heroID = player->HeroID;
        auto heroIt = heroIDToName.find(data.heroID);
        if (heroIt != heroIDToName.end()) {
            data.heroName = heroIt->second;
        } else {
            data.heroName = "Unknown Hero";
        }

        // Verificar visibilidade
        data.isVisible = mesh->WasRecentlyRendered(SDK::ERecentlyRenderedType::OnScreen, 0.0f);
        if (mods::UseLineOfSight) {
            data.isVisible = Modules::IsPlayerVisible(controller, player, headPos3D);
        }

        // Verificar se é inimigo
        data.isEnemy = true;
        if (Config::teamCheck && IsValidObjectPtr(Variables::AcknowledgedPawn)) {
            if (IsValidObjectPtr(player->PlayerState) && IsValidObjectPtr(Variables::AcknowledgedPawn->PlayerState)) {
                data.isEnemy = !SDK::UTeamFunctionLibrary::IsAlly(Variables::AcknowledgedPawn, player, true);
            }
        }

        // Obter porcentagem de ultimate
        data.ultPercentage = 0.0f;
        try {
            SDK::AThreatValueAdmin* threatAdmin = SDK::UMarvelAudioLibrary::GetThreatValueAdmin(Variables::World);
            if (IsValidObjectPtr(threatAdmin)) {
                auto threatInfoArray = threatAdmin->GetPlayerThreatInfo();
                for (const auto& threatInfo : threatInfoArray) {
                    if (threatInfo.Character == player) {
                        data.ultPercentage = threatInfo.UltimatePercentage;
                        break;
                    }
                }
            }
        } catch (...) {
            // Falha silenciosa
        }

        return data;
    }

    //---------------------------------------------------------------------
    // 		✅	Verificar se Deve Renderizar Jogador
    //---------------------------------------------------------------------
    bool ShouldRenderPlayer(SDK::AMarvelBaseCharacter* player, SDK::APlayerController* controller) {
        if (!IsValidObjectPtr(player) || !IsValidPtr(controller)) {
            return false;
        }

        // Verificar se tem saúde
        if (player->GetCurrentHealth() <= 0.0f) {
            return false;
        }

        // Verificar se é jogador local
        if (player->IsLocallyControlled()) {
            return false;
        }

        // Verificar distância
        float distance = SDK::UKismetMathLibrary::Vector_Distance(Variables::CameraLocation, player->K2_GetActorLocation()) / 100.0f;
        if (distance > Config::maxDistance) {
            return false;
        }

        // Verificar se é jogador real (não NPC)
        if (!IsValidObjectPtr(player->PlayerState)) {
            return false;
        }

        return true;
    }

    //---------------------------------------------------------------------
    // 		🦴	Renderizar Skeleton ESP
    //---------------------------------------------------------------------
    void RenderSkeleton(SDK::AMarvelBaseCharacter* player, ImDrawList* drawList, SDK::APlayerController* controller) {
        if (!IsValidPtr(drawList) || !IsValidObjectPtr(player) || !IsValidPtr(controller)) {
            return;
        }

        SDK::USkeletalMeshComponent* mesh = player->GetMesh();
        if (!IsValidObjectPtr(mesh)) {
            return;
        }

        for (const auto& [boneNameStr, parentBoneNameStr] : boneConnections) {
            SDK::FName boneName = SDK::UKismetStringLibrary::Conv_StringToName(SDK::FString(boneNameStr.c_str()));
            if (!mesh->DoesSocketExist(boneName)) continue;

            SDK::FVector boneWorldPos = mesh->GetSocketLocation(boneName);
            SDK::FVector2D boneScreenPos;
            if (!controller->ProjectWorldLocationToScreen(boneWorldPos, &boneScreenPos, true)) continue;

            if (!parentBoneNameStr.empty()) {
                SDK::FName parentBoneName = SDK::UKismetStringLibrary::Conv_StringToName(SDK::FString(parentBoneNameStr.c_str()));
                if (mesh->DoesSocketExist(parentBoneName)) {
                    SDK::FVector parentBoneWorldPos = mesh->GetSocketLocation(parentBoneName);
                    SDK::FVector2D parentBoneScreenPos;

                    if (controller->ProjectWorldLocationToScreen(parentBoneWorldPos, &parentBoneScreenPos, true)) {
                        drawList->AddLine(
                            ImVec2(boneScreenPos.X, boneScreenPos.Y),
                            ImVec2(parentBoneScreenPos.X, parentBoneScreenPos.Y),
                            Config::skeletonColor,
                            Config::skeletonThickness);
                    }
                }
            }
        }
    }

    //---------------------------------------------------------------------
    // 		📏	Renderizar Tracers
    //---------------------------------------------------------------------
    void RenderTracers(const ESPRenderData& data, ImDrawList* drawList) {
        if (!IsValidPtr(drawList)) return;

        ImVec2 displaySize = ImGui::GetIO().DisplaySize;
        ImVec2 startPos;

        switch (Config::tracerStartPos) {
            case TRACER_TOP:
                startPos = ImVec2(displaySize.x / 2, 0);
                break;
            case TRACER_CENTER:
                startPos = ImVec2(displaySize.x / 2, displaySize.y / 2);
                break;
            case TRACER_BOTTOM:
                startPos = ImVec2(displaySize.x / 2, displaySize.y);
                break;
        }

        drawList->AddLine(startPos, ImVec2(data.headPos.X, data.headPos.Y),
                         Config::tracerColor, Config::tracerThickness);
    }

    //---------------------------------------------------------------------
    // 		🎯	Renderizar Crosshair
    //---------------------------------------------------------------------
    void RenderCrosshair(ImDrawList* drawList) {
        if (!IsValidPtr(drawList) || Config::crosshairType == CROSSHAIR_NONE) return;

        ImVec2 displaySize = ImGui::GetIO().DisplaySize;
        ImVec2 center = ImVec2(displaySize.x / 2, displaySize.y / 2);

        switch (Config::crosshairType) {
            case CROSSHAIR_DOT:
                drawList->AddCircleFilled(center, Config::crosshairSize, Config::crosshairColor);
                break;
            case CROSSHAIR_CROSS: {
                float halfSize = Config::crosshairSize / 2;
                drawList->AddLine(ImVec2(center.x - halfSize, center.y),
                                ImVec2(center.x + halfSize, center.y),
                                Config::crosshairColor, Config::crosshairThickness);
                drawList->AddLine(ImVec2(center.x, center.y - halfSize),
                                ImVec2(center.x, center.y + halfSize),
                                Config::crosshairColor, Config::crosshairThickness);
                break;
            }
            case CROSSHAIR_CIRCLE:
                drawList->AddCircle(center, Config::crosshairSize, Config::crosshairColor,
                                  0, Config::crosshairThickness);
                break;
        }
    }

    //---------------------------------------------------------------------
    // 		🎯	Renderizar Snap Line
    //---------------------------------------------------------------------
    void RenderSnapLine(SDK::AMarvelBaseCharacter* target, ImDrawList* drawList, SDK::APlayerController* controller) {
        if (!IsValidPtr(drawList) || !IsValidObjectPtr(target) || !IsValidPtr(controller)) {
            return;
        }

        SDK::USkeletalMeshComponent* mesh = target->GetMesh();
        if (!IsValidObjectPtr(mesh)) {
            return;
        }

        SDK::FName boneName = SDK::UKismetStringLibrary::Conv_StringToName(SDK::FString(L"head"));
        if (!mesh->DoesSocketExist(boneName)) {
            return;
        }

        SDK::FVector targetBone3D = mesh->GetSocketLocation(boneName);
        SDK::FVector2D targetBone2D;
        if (controller->ProjectWorldLocationToScreen(targetBone3D, &targetBone2D, true)) {
            ImVec2 displaySize = ImGui::GetIO().DisplaySize;
            ImVec2 screenCenter = ImVec2(displaySize.x / 2, displaySize.y / 2);
            ImVec2 targetPos = ImVec2(targetBone2D.X, targetBone2D.Y);

            drawList->AddLine(screenCenter, targetPos, Config::snapLineColor, Config::snapLineThickness);
        }
    }

    //---------------------------------------------------------------------
    // 		✨	Aplicar Glow
    //---------------------------------------------------------------------
    void ApplyGlow(SDK::AMarvelBaseCharacter* player, bool isEnemy) {
        if (!IsValidObjectPtr(player)) return;

        if (Config::glowIgnoreTeammates && !isEnemy) return;

        SDK::USkeletalMeshComponent* mesh = player->GetMesh();
        if (!IsValidObjectPtr(mesh)) return;

        SDK::ETeamOutlineShowStatus status = Config::glowThroughWalls ?
            SDK::ETeamOutlineShowStatus::ETOSS_Always :
            SDK::ETeamOutlineShowStatus::ETOSS_Unoccluded;

        mesh->SetTeamOutlineShowStatus(status);
    }

    void LoadDefaultConfig() {
        // Configurações já inicializadas com valores padrão
    }
}
