#include "WarningSuppressions.h" // Supressão de warnings para todo o projeto

#include "main.h"
#include "./Game/SDK/SDK/CoreUObject_structs.hpp"
#include "./Game/SDK/SDK/Marvel_classes.hpp"
using namespace SDK;
#include "Globals.h"
#include "OverlayRenderer.h"
#include "AnimatedWidgets.h" // Incluir os widgets animados
#include "KeyCaptureSystem.h" // Sistema de captura de teclas
#include "montserrat_font.h" // Fonte Montserrat Regular
#include "montserrat_bold_font.h" // Fonte Montserrat Bold
#include <thread>
#include <fstream>			// Para trabalhar com arquivos
#include <iostream>			// Para entrada/saída
#include <sstream>			// Para stringstream
#include <direct.h>			// Para _mkdir
#include <filesystem>		// Para std::filesystem
#include "LanguageSystem.h" // Sistema de idiomas

#ifdef _MSC_VER
#pragma warning(push) // Restaurar avisos após incluir arquivos de terceiros
#endif

// Constantes de cores dos temas personalizados
// NOTA: Alpha 0.99f em vez de 1.0f para compatibilidade com drivers AMD
namespace ThemeColors {
	// Tema Azul Escuro com Detalhes Dourados (Padrão)
	namespace DarkBlueGold {
		const ImVec4 GOLD_TEXT = ImVec4(1.0f, 0.84f, 0.0f, 0.99f);        // Texto dourado
		const ImVec4 GREEN_INFO = ImVec4(0.0f, 0.7f, 0.0f, 0.99f);        // Verde para informações
		const ImVec4 ORANGE_WARNING = ImVec4(1.0f, 0.5f, 0.0f, 0.99f);    // Laranja para avisos
		const ImVec4 YELLOW_HIGHLIGHT = ImVec4(1.0f, 1.0f, 0.0f, 0.99f);  // Amarelo para destaques
		const ImVec4 BLACK_SHADOW = ImVec4(0.0f, 0.0f, 0.0f, 0.99f);      // Preto para sombras
		const ImVec4 CYAN_CURRENT = ImVec4(0.0f, 1.0f, 1.0f, 0.99f);     // Ciano para item atual
	}

	// Tema Laranja/Dourado (Secundário)
	namespace OrangeGold {
		const ImVec4 GOLD_TEXT = ImVec4(1.0f, 0.84f, 0.0f, 0.99f);        // Texto dourado
		const ImVec4 GREEN_INFO = ImVec4(0.0f, 0.7f, 0.0f, 0.99f);        // Verde para informações
		const ImVec4 ORANGE_WARNING = ImVec4(1.0f, 0.5f, 0.0f, 0.99f);    // Laranja para avisos
		const ImVec4 YELLOW_HIGHLIGHT = ImVec4(1.0f, 1.0f, 0.0f, 0.99f);  // Amarelo para destaques
		const ImVec4 BLACK_SHADOW = ImVec4(0.0f, 0.0f, 0.0f, 0.99f);      // Preto para sombras
		const ImVec4 CYAN_CURRENT = ImVec4(0.0f, 1.0f, 1.0f, 0.99f);     // Ciano para item atual
	}

	// Cores ativas (apontam para o tema atual)
	const ImVec4* GOLD_TEXT = &DarkBlueGold::GOLD_TEXT;
	const ImVec4* GREEN_INFO = &DarkBlueGold::GREEN_INFO;
	const ImVec4* ORANGE_WARNING = &DarkBlueGold::ORANGE_WARNING;
	const ImVec4* YELLOW_HIGHLIGHT = &DarkBlueGold::YELLOW_HIGHLIGHT;
	const ImVec4* BLACK_SHADOW = &DarkBlueGold::BLACK_SHADOW;
	const ImVec4* CYAN_CURRENT = &DarkBlueGold::CYAN_CURRENT;
}

//---------------------------------------------------------------------
// 		🔤	Funções Auxiliares para Fontes
//---------------------------------------------------------------------

// Função para obter a fonte Bold (segunda fonte carregada)
ImFont* GetMontserratBoldFont()
{
	ImGuiIO& io = ImGui::GetIO();
	if (io.Fonts->Fonts.Size > 1 && io.Fonts->Fonts[1] != nullptr)
	{
		return io.Fonts->Fonts[1]; // Montserrat Bold é a segunda fonte
	}
	return nullptr; // Fallback para fonte padrão
}

// Função para texto com fonte Bold (para títulos)
void BoldText(const char* texto)
{
	ImFont* boldFont = GetMontserratBoldFont();
	if (boldFont != nullptr)
	{
		ImGui::PushFont(boldFont);
		ImGui::Text("%s", texto);
		ImGui::PopFont();
	}
	else
	{
		ImGui::Text("%s", texto); // Fallback para fonte padrão
	}
}

// Função para texto dourado com fonte Bold (para títulos principais)
void GoldBoldText(const char* texto)
{
	ImFont* boldFont = GetMontserratBoldFont();
	if (boldFont != nullptr)
	{
		ImGui::PushFont(boldFont);
		ImGui::TextColored(*ThemeColors::GOLD_TEXT, "%s", texto);
		ImGui::PopFont();
	}
	else
	{
		ImGui::TextColored(*ThemeColors::GOLD_TEXT, "%s", texto); // Fallback
	}
}

// Função para texto dourado com sombra e fonte Bold (para títulos de seções)
void GoldBoldTextWithShadow(const char* texto)
{
	ImFont* boldFont = GetMontserratBoldFont();
	if (boldFont != nullptr)
	{
		ImGui::PushFont(boldFont);

		// Desenhar sombra
		ImVec2 pos = ImGui::GetCursorPos();
		ImGui::SetCursorPos(ImVec2(pos.x + 1, pos.y + 1));
		ImGui::TextColored(*ThemeColors::BLACK_SHADOW, "%s", texto);

		// Desenhar texto principal
		ImGui::SetCursorPos(pos);
		ImGui::TextColored(*ThemeColors::GOLD_TEXT, "%s", texto);

		ImGui::PopFont();
	}
	else
	{
		// Fallback com sombra usando fonte padrão
		ImVec2 pos = ImGui::GetCursorPos();
		ImGui::SetCursorPos(ImVec2(pos.x + 1, pos.y + 1));
		ImGui::TextColored(*ThemeColors::BLACK_SHADOW, "%s", texto);
		ImGui::SetCursorPos(pos);
		ImGui::TextColored(*ThemeColors::GOLD_TEXT, "%s", texto);
	}
}

// Função para adicionar tooltips explicativos
void AddTooltip(const char *desc)
{
	ImGui::SameLine();
	ImGui::TextColored(*ThemeColors::YELLOW_HIGHLIGHT, "(?)");
	if (ImGui::IsItemHovered())
	{
		ImGui::BeginTooltip();
		ImGui::PushTextWrapPos(ImGui::GetFontSize() * 35.0f);
		ImGui::TextUnformatted(desc);
		ImGui::PopTextWrapPos();
		ImGui::EndTooltip();
	}
}

// Função para adicionar tooltips usando o sistema de idiomas
void AddTooltipLocalized(TextID id)
{
	AddTooltip(LanguageSystem::Get().GetText(id));
}

// Função para exibir texto com cor dourada e borda preta (fonte regular)
void GoldTextWithShadow(const char *text)
{
	// Calcular a posição atual
	ImVec2 pos = ImGui::GetCursorPos();

	// Desenhar o texto com sombra (offset em várias direções para criar uma borda preta)
	ImGui::SetCursorPos(ImVec2(pos.x + 1, pos.y + 1));
	ImGui::TextColored(*ThemeColors::BLACK_SHADOW, text);
	ImGui::SetCursorPos(ImVec2(pos.x - 1, pos.y + 1));
	ImGui::TextColored(*ThemeColors::BLACK_SHADOW, text);
	ImGui::SetCursorPos(ImVec2(pos.x + 1, pos.y - 1));
	ImGui::TextColored(*ThemeColors::BLACK_SHADOW, text);
	ImGui::SetCursorPos(ImVec2(pos.x - 1, pos.y - 1));
	ImGui::TextColored(*ThemeColors::BLACK_SHADOW, text);

	// Desenhar o texto dourado por cima
	ImGui::SetCursorPos(pos);
	ImGui::TextColored(*ThemeColors::GOLD_TEXT, text);
}

int countnum = -1;

// Diretório para salvar as configurações
const char *CONFIG_DIR = "JocastaConfig";
const char *DEFAULT_CONFIG = "default.cfg";
const char *LAST_PROFILE_FILE = "last_profile.txt";
char currentConfigFile[256] = "default.cfg";
bool configLoaded = false;

// Função para garantir que o diretório de configurações exista
void EnsureConfigDirectoryExists()
{
	// Criar o diretório se não existir
	_mkdir(CONFIG_DIR);
}

// Declaração antecipada da função para salvar o nome do último perfil utilizado
void SaveLastUsedProfile(const char *profileName);

// Declaração antecipada da função para carregar o nome do último perfil utilizado
bool LoadLastUsedProfile(char *profileName, size_t bufferSize);

// Função para aplicar o tema selecionado
void ApplyTheme(UITheme theme);

// Função para salvar as configurações em um arquivo
bool SaveConfig(const char *filename)
{
	EnsureConfigDirectoryExists();

	char fullPath[512];
	sprintf_s(fullPath, "%s/%s", CONFIG_DIR, filename);

	std::ofstream file(fullPath);
	if (!file.is_open())
		return false;

	// Salvar as configurações
	file << "[Aimbot]" << std::endl;
	file << "aimbot=" << (mods::aimbot ? "1" : "0") << std::endl;
	file << "smoothAmount=" << mods::smoothAmount << std::endl;
	file << "separatePitchYawSmoothing=" << (mods::separatePitchYawSmoothing ? "1" : "0") << std::endl;
	file << "smoothAmountPitch=" << mods::smoothAmountPitch << std::endl;
	file << "smoothAmountYaw=" << mods::smoothAmountYaw << std::endl;
	file << "useNeckTarget=" << (mods::useNeckTarget ? "1" : "0") << std::endl;
	file << "fov=" << mods::fov << std::endl;
	file << "fov_circle=" << (mods::aimbotFovCircle ? "1" : "0") << std::endl;
	file << "aimbot_key=" << mods::aimbot_key << std::endl;
	file << "gamepad_aimbot_button=" << mods::gamepad_aimbot_button << std::endl;
	file << "focusLowestHealth=" << (mods::focusLowestHealth ? "1" : "0") << std::endl;

	// Sistema de inércia
	file << "useInertia=" << (mods::useInertia ? "1" : "0") << std::endl;
	file << "inertiaFactor=" << mods::inertiaFactor << std::endl;

	// Sistema de movimento adaptativo
	file << "useAdaptiveMovement=" << (mods::useAdaptiveMovement ? "1" : "0") << std::endl;
	file << "adaptiveDuration=" << mods::adaptiveDuration << std::endl;
	file << "initialSmoothAmount=" << mods::initialSmoothAmount << std::endl;
	file << "minSmoothAmount=" << mods::minSmoothAmount << std::endl;
	file << "separateAdaptiveSettings=" << (mods::separateAdaptiveSettings ? "1" : "0") << std::endl;
	file << "initialSmoothAmountPitch=" << mods::initialSmoothAmountPitch << std::endl;
	file << "minSmoothAmountPitch=" << mods::minSmoothAmountPitch << std::endl;
	file << "initialSmoothAmountYaw=" << mods::initialSmoothAmountYaw << std::endl;
	file << "minSmoothAmountYaw=" << mods::minSmoothAmountYaw << std::endl;
	file << "useAdaptiveInertia=" << (mods::useAdaptiveInertia ? "1" : "0") << std::endl;
	file << "initialInertiaFactor=" << mods::initialInertiaFactor << std::endl;
	file << "minInertiaFactor=" << mods::minInertiaFactor << std::endl;
	file << "useFlick=" << (mods::useFlick ? "1" : "0") << std::endl;
	file << "flickDuration=" << mods::flickDuration << std::endl;
	file << "flickReductionType=" << mods::flickReductionType << std::endl;

	// Sistema de humanização (melhorado baseado no projeto de exemplo)
	file << "targetBodyPartMode=" << mods::targetBodyPartMode << std::endl;
	file << "useHumanizedTargeting=" << (mods::useHumanizedTargeting ? "1" : "0") << std::endl;
	file << "headProbability=" << mods::headProbability << std::endl;
	file << "neckProbability=" << mods::neckProbability << std::endl;
	file << "chestProbability=" << mods::chestProbability << std::endl;
	file << "spineProbability=" << mods::spineProbability << std::endl;
	file << "pelvisProbability=" << mods::pelvisProbability << std::endl;
	file << "bodyPartUpdateInterval=" << mods::bodyPartUpdateInterval << std::endl;
	file << "bodyPartChangeMinTime=" << mods::bodyPartChangeMinTime << std::endl;
	file << "bodyPartChangeMaxTime=" << mods::bodyPartChangeMaxTime << std::endl;

	// Configurações de delay
	file << "useAimDelay=" << (mods::useAimDelay ? "1" : "0") << std::endl;
	file << "aimDelayAmount=" << mods::aimDelayAmount << std::endl;

	// Configurações de Team Target Mode
	file << "teamTargetMode=" << (mods::teamTargetMode ? "1" : "0") << std::endl;
	file << "teamTargetKey=" << mods::teamTargetKey << std::endl;
	file << "gamepadTeamTargetButton=" << mods::gamepadTeamTargetButton << std::endl;

	// Configurações de Visão de Suporte
	file << "isSupportVisionEnabled=" << (mods::isSupportVisionEnabled ? "1" : "0") << std::endl;

	// Configurações de First Enemy Lock
	file << "firstEnemyLock=" << (mods::firstEnemyLock ? "1" : "0") << std::endl;

	// Configurações de Target Invisible Enemies
	file << "targetInvisibleEnemies=" << (mods::targetInvisibleEnemies ? "1" : "0") << std::endl;

	// Configurações de Priorização de Alvos Voadores
	file << "shouldPrioritizeFlyingTargets=" << (mods::shouldPrioritizeFlyingTargets ? "1" : "0") << std::endl;
	file << "flyingVelocityThreshold=" << mods::flyingVelocityThreshold << std::endl;

	// TEMPORARIAMENTE REMOVIDO: Configurações de Priorização de Summons/Construtos
	// file << "shouldPrioritizeSummons=" << (mods::shouldPrioritizeSummons ? "1" : "0") << std::endl;
	// file << "shouldPrioritizeConstructs=" << (mods::shouldPrioritizeConstructs ? "1" : "0") << std::endl;

	file << "\n[BulletTP]" << std::endl;
	file << "bullet_tp=" << (mods::bullet_tp ? "1" : "0") << std::endl;
	file << "bullet_tp_target=" << mods::bullet_tp_target << std::endl;
	file << "bullet_tp_fov_circle=" << (mods::bullet_tp_fov_circle ? "1" : "0") << std::endl;
	file << "bullet_tp_fov=" << mods::bullet_tp_fov << std::endl;
	file << "bullet_tp_key=" << mods::bullet_tp_key << std::endl;
	file << "gamepad_bullet_tp_button=" << mods::gamepad_bullet_tp_button << std::endl;
	file << "bulletTPContinueAfterKeyUp=" << (mods::bulletTPContinueAfterKeyUp ? "1" : "0") << std::endl;
	file << "bulletTPKeyUpDuration=" << mods::bulletTPKeyUpDuration << std::endl;
	file << "bulletTPDebug=" << (mods::bulletTPDebug ? "1" : "0") << std::endl;

	file << "\n[ESP]" << std::endl;
	file << "esp=" << (mods::esp ? "1" : "0") << std::endl;
	file << "ShowHealth=" << (mods::ShowHealth ? "1" : "0") << std::endl;
	file << "ShowDistance=" << (mods::ShowDistance ? "1" : "0") << std::endl;
	file << "TracerLines=" << (mods::TracerLines ? "1" : "0") << std::endl;
	file << "TeamCheck=" << (mods::TeamCheck ? "1" : "0") << std::endl;
	file << "VisCheck=" << (mods::VisCheck ? "1" : "0") << std::endl;
	file << "UseLineOfSight=" << (mods::UseLineOfSight ? "1" : "0") << std::endl;
	file << "LocalCheck=" << (mods::LocalCheck ? "1" : "0") << std::endl;

	// ESP Moderno
	file << "showBoxes=" << (mods::showBoxes ? "1" : "0") << std::endl;
	file << "boxType=" << mods::boxType << std::endl;
	file << "boxOutline=" << (mods::boxOutline ? "1" : "0") << std::endl;
	file << "boxThickness=" << mods::boxThickness << std::endl;
	file << "showHealthBar=" << (mods::showHealthBar ? "1" : "0") << std::endl;
	file << "healthBarPosition=" << mods::healthBarPosition << std::endl;
	file << "showSkeleton=" << (mods::showSkeleton ? "1" : "0") << std::endl;
	file << "skeletonThickness=" << mods::skeletonThickness << std::endl;
	file << "showPlayerNames=" << (mods::showPlayerNames ? "1" : "0") << std::endl;
	file << "showCrosshair=" << (mods::showCrosshair ? "1" : "0") << std::endl;
	file << "crosshairType=" << mods::crosshairType << std::endl;
	file << "crosshairSize=" << mods::crosshairSize << std::endl;
	file << "crosshairThickness=" << mods::crosshairThickness << std::endl;
	file << "showSnapLines=" << (mods::showSnapLines ? "1" : "0") << std::endl;
	file << "maxESPDistance=" << mods::maxESPDistance << std::endl;
	file << "showSummonsESP=" << (mods::showSummonsESP ? "1" : "0") << std::endl;

	file << "\n[Glow]" << std::endl;
	file << "enableGlow=" << (mods::enableGlow ? "1" : "0") << std::endl;
	file << "showUltimatePercentage=" << (mods::showUltimatePercentage ? "1" : "0") << std::endl;

	file << "\n[RoleTarget]" << std::endl;
	file << "enableRoleTarget=" << (mods::enableRoleTarget ? "1" : "0") << std::endl;
	file << "targetRole=" << mods::targetRole << std::endl;

	file << "\n[Misc]" << std::endl;
	file << "fov_changer=" << (mods::fov_changer ? "1" : "0") << std::endl;
	file << "fov_changer_amount=" << mods::fov_changer_amount << std::endl;
	file << "currentLanguage=" << mods::currentLanguage << std::endl;
	file << "currentTheme=" << mods::currentTheme << std::endl;

	file << "\n[UIZoom]" << std::endl;
	file << "enableUIZoom=" << (mods::enableUIZoom ? "1" : "0") << std::endl;
	file << "uiZoomFactor=" << mods::uiZoomFactor << std::endl;
	file << "uiZoomStep=" << mods::uiZoomStep << std::endl;
	file << "uiZoomMin=" << mods::uiZoomMin << std::endl;
	file << "uiZoomMax=" << mods::uiZoomMax << std::endl;

	file.close();
	return true;
}

// Função para carregar as configurações de um arquivo
bool LoadConfig(const char *filename)
{
	char fullPath[512];
	sprintf_s(fullPath, "%s/%s", CONFIG_DIR, filename);

	std::ifstream file(fullPath);
	if (!file.is_open())
		return false;

	std::string line;
	std::string section;

	while (std::getline(file, line))
	{
		// Ignorar linhas vazias
		if (line.empty())
			continue;

		// Verificar se é uma seção
		if (line[0] == '[' && line[line.length() - 1] == ']')
		{
			section = line.substr(1, line.length() - 2);
			continue;
		}

		// Dividir a linha em chave e valor
		size_t pos = line.find('=');
		if (pos == std::string::npos)
			continue;

		std::string key = line.substr(0, pos);
		std::string value = line.substr(pos + 1);

		// Processar as configurações com base na seção
		if (section == "Aimbot")
		{
			if (key == "aimbot")
				mods::aimbot = (value == "1");
			else if (key == "smoothAmount")
				mods::smoothAmount = std::stof(value);
			else if (key == "separatePitchYawSmoothing")
				mods::separatePitchYawSmoothing = (value == "1");
			else if (key == "smoothAmountPitch")
				mods::smoothAmountPitch = std::stof(value);
			else if (key == "smoothAmountYaw")
				mods::smoothAmountYaw = std::stof(value);
			else if (key == "useNeckTarget")
				mods::useNeckTarget = (value == "1");
			else if (key == "fov")
				mods::fov = std::stoi(value);
			else if (key == "fov_circle")
				mods::aimbotFovCircle = (value == "1");
			else if (key == "aimbot_key")
				mods::aimbot_key = std::stoi(value);
			else if (key == "gamepad_aimbot_button")
				mods::gamepad_aimbot_button = std::stoi(value);
			else if (key == "focusLowestHealth")
				mods::focusLowestHealth = (value == "1");
			// Configurações de inércia
			else if (key == "useInertia")
				mods::useInertia = (value == "1");
			else if (key == "inertiaFactor")
				mods::inertiaFactor = std::stof(value);
			// Configurações de delay
			else if (key == "useAimDelay")
				mods::useAimDelay = (value == "1");
			else if (key == "aimDelayAmount")
				mods::aimDelayAmount = std::stof(value);
			// Configurações de Team Target Mode
			else if (key == "teamTargetMode")
				mods::teamTargetMode = (value == "1");
			else if (key == "teamTargetKey")
				mods::teamTargetKey = std::stoi(value);
			else if (key == "gamepadTeamTargetButton")
				mods::gamepadTeamTargetButton = std::stoi(value);
			// Configurações de Visão de Suporte
			else if (key == "isSupportVisionEnabled")
				mods::isSupportVisionEnabled = (value == "1");
			// Configurações de First Enemy Lock
			else if (key == "firstEnemyLock")
				mods::firstEnemyLock = (value == "1");
			// Configurações de Target Invisible Enemies
			else if (key == "targetInvisibleEnemies")
				mods::targetInvisibleEnemies = (value == "1");
			// Configurações de Priorização de Alvos Voadores
			else if (key == "shouldPrioritizeFlyingTargets")
				mods::shouldPrioritizeFlyingTargets = (value == "1");
			else if (key == "flyingVelocityThreshold")
				mods::flyingVelocityThreshold = std::stof(value);
			// TEMPORARIAMENTE REMOVIDO: Configurações de Priorização de Summons/Construtos
			// else if (key == "shouldPrioritizeSummons")
			//     mods::shouldPrioritizeSummons = (value == "1");
			// else if (key == "shouldPrioritizeConstructs")
			//     mods::shouldPrioritizeConstructs = (value == "1");
			// Configurações de movimento adaptativo
			else if (key == "useAdaptiveMovement")
				mods::useAdaptiveMovement = (value == "1");
			else if (key == "adaptiveDuration")
				mods::adaptiveDuration = std::stof(value);
			else if (key == "initialSmoothAmount")
				mods::initialSmoothAmount = std::stof(value);
			else if (key == "minSmoothAmount")
				mods::minSmoothAmount = std::stof(value);
			else if (key == "separateAdaptiveSettings")
				mods::separateAdaptiveSettings = (value == "1");
			else if (key == "initialSmoothAmountPitch")
				mods::initialSmoothAmountPitch = std::stof(value);
			else if (key == "minSmoothAmountPitch")
				mods::minSmoothAmountPitch = std::stof(value);
			else if (key == "initialSmoothAmountYaw")
				mods::initialSmoothAmountYaw = std::stof(value);
			else if (key == "minSmoothAmountYaw")
				mods::minSmoothAmountYaw = std::stof(value);
			else if (key == "useAdaptiveInertia")
				mods::useAdaptiveInertia = (value == "1");
			else if (key == "initialInertiaFactor")
				mods::initialInertiaFactor = std::stof(value);
			else if (key == "minInertiaFactor")
				mods::minInertiaFactor = std::stof(value);
			// Configurações do sistema Flick
			else if (key == "useFlick")
				mods::useFlick = (value == "1");
			else if (key == "flickDuration")
				mods::flickDuration = std::stof(value);
			else if (key == "flickReductionType")
				mods::flickReductionType = std::stoi(value);
			// Configurações de humanização (melhorado baseado no projeto de exemplo)
			else if (key == "targetBodyPartMode")
				mods::targetBodyPartMode = std::stoi(value);
			else if (key == "useHumanizedTargeting")
				mods::useHumanizedTargeting = (value == "1");
			else if (key == "headProbability")
				mods::headProbability = std::stof(value);
			else if (key == "neckProbability")
				mods::neckProbability = std::stof(value);
			else if (key == "chestProbability")
				mods::chestProbability = std::stof(value);
			else if (key == "spineProbability")
				mods::spineProbability = std::stof(value);
			else if (key == "pelvisProbability")
				mods::pelvisProbability = std::stof(value);
			else if (key == "bodyPartUpdateInterval")
				mods::bodyPartUpdateInterval = std::stof(value);
			else if (key == "bodyPartChangeMinTime")
				mods::bodyPartChangeMinTime = std::stof(value);
			else if (key == "bodyPartChangeMaxTime")
				mods::bodyPartChangeMaxTime = std::stof(value);
		}
		else if (section == "BulletTP")
		{
			if (key == "bullet_tp")
				mods::bullet_tp = (value == "1");
			else if (key == "bullet_tp_target")
				mods::bullet_tp_target = std::stoi(value);
			else if (key == "bullet_tp_fov_circle")
				mods::bullet_tp_fov_circle = (value == "1");
			else if (key == "bullet_tp_fov")
				mods::bullet_tp_fov = std::stoi(value);
			else if (key == "bullet_tp_key")
				mods::bullet_tp_key = std::stoi(value);
			else if (key == "gamepad_bullet_tp_button")
				mods::gamepad_bullet_tp_button = std::stoi(value);
			else if (key == "bulletTPContinueAfterKeyUp")
				mods::bulletTPContinueAfterKeyUp = (value == "1");
			else if (key == "bulletTPKeyUpDuration")
				mods::bulletTPKeyUpDuration = std::stof(value);
			else if (key == "bulletTPDebug")
				mods::bulletTPDebug = (value == "1");
		}

		else if (section == "ESP")
		{
			if (key == "esp")
				mods::esp = (value == "1");
			else if (key == "ShowHealth")
				mods::ShowHealth = (value == "1");
			else if (key == "ShowDistance")
				mods::ShowDistance = (value == "1");
			else if (key == "TracerLines")
				mods::TracerLines = (value == "1");
			else if (key == "TeamCheck")
				mods::TeamCheck = (value == "1");
			else if (key == "VisCheck")
				mods::VisCheck = (value == "1");
			else if (key == "UseLineOfSight")
				mods::UseLineOfSight = (value == "1");
			else if (key == "LocalCheck")
				mods::LocalCheck = (value == "1");
			// ESP Moderno
			else if (key == "showBoxes")
				mods::showBoxes = (value == "1");
			else if (key == "boxType")
				mods::boxType = std::stoi(value);
			else if (key == "boxOutline")
				mods::boxOutline = (value == "1");
			else if (key == "boxThickness")
				mods::boxThickness = std::stof(value);
			else if (key == "showHealthBar")
				mods::showHealthBar = (value == "1");
			else if (key == "healthBarPosition")
				mods::healthBarPosition = std::stoi(value);
			else if (key == "showSkeleton")
				mods::showSkeleton = (value == "1");
			else if (key == "skeletonThickness")
				mods::skeletonThickness = std::stof(value);
			else if (key == "showPlayerNames")
				mods::showPlayerNames = (value == "1");
			else if (key == "showCrosshair")
				mods::showCrosshair = (value == "1");
			else if (key == "crosshairType")
				mods::crosshairType = std::stoi(value);
			else if (key == "crosshairSize")
				mods::crosshairSize = std::stof(value);
			else if (key == "crosshairThickness")
				mods::crosshairThickness = std::stof(value);
			else if (key == "showSnapLines")
				mods::showSnapLines = (value == "1");
			else if (key == "maxESPDistance")
				mods::maxESPDistance = std::stof(value);
			else if (key == "showSummonsESP")
				mods::showSummonsESP = (value == "1");
		}
		else if (section == "Glow")
		{
			if (key == "enableGlow")
				mods::enableGlow = (value == "1");
			else if (key == "showUltimatePercentage")
				mods::showUltimatePercentage = (value == "1");
		}
		else if (section == "RoleTarget")
		{
			if (key == "enableRoleTarget")
				mods::enableRoleTarget = (value == "1");
			else if (key == "targetRole")
				mods::targetRole = std::stoi(value);
		}
		else if (section == "Misc")
		{
			if (key == "fov_changer")
				mods::fov_changer = (value == "1");
			else if (key == "fov_changer_amount")
				mods::fov_changer_amount = std::stoi(value);
			else if (key == "currentLanguage")
				mods::currentLanguage = std::stoi(value);
			else if (key == "currentTheme")
				mods::currentTheme = std::stoi(value);
		}
		else if (section == "UIZoom")
		{
			if (key == "enableUIZoom")
				mods::enableUIZoom = (value == "1");
			else if (key == "uiZoomFactor")
				mods::uiZoomFactor = std::stof(value);
			else if (key == "uiZoomStep")
				mods::uiZoomStep = std::stof(value);
			else if (key == "uiZoomMin")
				mods::uiZoomMin = std::stof(value);
			else if (key == "uiZoomMax")
				mods::uiZoomMax = std::stof(value);
		}
	}

	file.close();
	strcpy_s(currentConfigFile, filename);

	// Salvar o nome do perfil como o último utilizado
	SaveLastUsedProfile(filename);

	return true;
}

// Função para listar todos os arquivos de configuração disponíveis
std::vector<std::string> ListConfigFiles()
{
	std::vector<std::string> files;
	EnsureConfigDirectoryExists();

	try
	{
		for (const auto &entry : std::filesystem::directory_iterator(CONFIG_DIR))
		{
			if (entry.is_regular_file() && entry.path().extension() == ".cfg")
			{
				files.push_back(entry.path().filename().string());
			}
		}
	}
	catch (const std::exception &)
	{
		// Silenciar exceções
	}

	// Se não houver arquivos, criar um arquivo padrão
	if (files.empty())
	{
		SaveConfig(DEFAULT_CONFIG);
		files.push_back(DEFAULT_CONFIG);
	}

	return files;
}

// Função para salvar o nome do último perfil utilizado
void SaveLastUsedProfile(const char *profileName)
{
	EnsureConfigDirectoryExists();

	char fullPath[512];
	sprintf_s(fullPath, "%s/%s", CONFIG_DIR, LAST_PROFILE_FILE);

	std::ofstream file(fullPath);
	if (file.is_open())
	{
		file << profileName;
		file.close();
	}
}

// Função para carregar o nome do último perfil utilizado
bool LoadLastUsedProfile(char *profileName, size_t bufferSize)
{
	char fullPath[512];
	sprintf_s(fullPath, "%s/%s", CONFIG_DIR, LAST_PROFILE_FILE);

	std::ifstream file(fullPath);
	if (file.is_open())
	{
		std::string lastProfile;
		std::getline(file, lastProfile);
		file.close();

		if (!lastProfile.empty())
		{
			// Verificar se o arquivo de perfil existe
			char profilePath[512];
			sprintf_s(profilePath, "%s/%s", CONFIG_DIR, lastProfile.c_str());
			std::ifstream profileFile(profilePath);
			if (profileFile.is_open())
			{
				profileFile.close();
				strncpy_s(profileName, bufferSize, lastProfile.c_str(), _TRUNCATE);
				return true;
			}
		}
	}
	return false;
}

// Função para aplicar o tema selecionado
void ApplyTheme(UITheme theme)
{
	// Atualizar as referências das cores ativas
	switch (theme)
	{
	case UITheme::DarkBlueGold:
		ThemeColors::GOLD_TEXT = &ThemeColors::DarkBlueGold::GOLD_TEXT;
		ThemeColors::GREEN_INFO = &ThemeColors::DarkBlueGold::GREEN_INFO;
		ThemeColors::ORANGE_WARNING = &ThemeColors::DarkBlueGold::ORANGE_WARNING;
		ThemeColors::YELLOW_HIGHLIGHT = &ThemeColors::DarkBlueGold::YELLOW_HIGHLIGHT;
		ThemeColors::BLACK_SHADOW = &ThemeColors::DarkBlueGold::BLACK_SHADOW;
		ThemeColors::CYAN_CURRENT = &ThemeColors::DarkBlueGold::CYAN_CURRENT;
		break;
	case UITheme::OrangeGold:
		ThemeColors::GOLD_TEXT = &ThemeColors::OrangeGold::GOLD_TEXT;
		ThemeColors::GREEN_INFO = &ThemeColors::OrangeGold::GREEN_INFO;
		ThemeColors::ORANGE_WARNING = &ThemeColors::OrangeGold::ORANGE_WARNING;
		ThemeColors::YELLOW_HIGHLIGHT = &ThemeColors::OrangeGold::YELLOW_HIGHLIGHT;
		ThemeColors::BLACK_SHADOW = &ThemeColors::OrangeGold::BLACK_SHADOW;
		ThemeColors::CYAN_CURRENT = &ThemeColors::OrangeGold::CYAN_CURRENT;
		break;
	}

	// Aplicar o estilo do ImGui baseado no tema
	ImGuiStyle &style = ImGui::GetStyle();

	if (theme == UITheme::DarkBlueGold)
	{
		// Tema Azul Escuro com Detalhes Dourados (Padrão)
		style.Colors[ImGuiCol_WindowBg] = ImVec4(0.08f, 0.12f, 0.18f, 0.95f);		// Fundo azul escuro
		style.Colors[ImGuiCol_ChildBg] = ImVec4(0.10f, 0.15f, 0.22f, 0.95f);		// Fundo de elementos filhos azul escuro
		style.Colors[ImGuiCol_PopupBg] = ImVec4(0.06f, 0.10f, 0.16f, 0.95f);		// Fundo de popups azul muito escuro
		style.Colors[ImGuiCol_Border] = ImVec4(0.20f, 0.35f, 0.60f, 0.50f);		// Bordas azuis
		style.Colors[ImGuiCol_BorderShadow] = ImVec4(0.00f, 0.00f, 0.00f, 0.00f);	// Sem sombra de borda
		style.Colors[ImGuiCol_FrameBg] = ImVec4(0.12f, 0.18f, 0.28f, 0.54f);		// Fundo de frames azul escuro
		style.Colors[ImGuiCol_FrameBgHovered] = ImVec4(0.18f, 0.28f, 0.42f, 0.40f); // Hover azul
		style.Colors[ImGuiCol_FrameBgActive] = ImVec4(0.25f, 0.40f, 0.65f, 0.67f);	// Ativo azul mais forte

		// Elementos de texto e títulos
		style.Colors[ImGuiCol_Text] = ImVec4(0.90f, 0.90f, 0.90f, 0.99f);			// Texto normal branco
		style.Colors[ImGuiCol_TextDisabled] = ImVec4(0.60f, 0.60f, 0.60f, 0.99f);	// Texto desativado cinza
		style.Colors[ImGuiCol_TextSelectedBg] = ImVec4(0.30f, 0.50f, 0.80f, 0.35f); // Fundo de texto selecionado azul

		// Cores do header da janela - azul escuro com detalhes dourados
		style.Colors[ImGuiCol_TitleBg] = ImVec4(0.15f, 0.25f, 0.40f, 0.9f);		  // Fundo do título azul escuro
		style.Colors[ImGuiCol_TitleBgActive] = ImVec4(0.20f, 0.35f, 0.60f, 0.99f);	  // Fundo do título ativo azul
		style.Colors[ImGuiCol_TitleBgCollapsed] = ImVec4(0.15f, 0.25f, 0.40f, 0.75f); // Fundo do título recolhido

		// Elementos interativos
		style.Colors[ImGuiCol_Header] = ImVec4(0.20f, 0.35f, 0.60f, 0.70f);		   // Cabeçalhos azuis
		style.Colors[ImGuiCol_HeaderHovered] = ImVec4(0.25f, 0.45f, 0.75f, 0.80f); // Hover de cabeçalhos azul mais claro
		style.Colors[ImGuiCol_HeaderActive] = ImVec4(0.30f, 0.55f, 0.90f, 0.99f);  // Cabeçalhos ativos azul brilhante

		// Botões com detalhes dourados
		style.Colors[ImGuiCol_Button] = ImVec4(0.15f, 0.25f, 0.40f, 0.70f);		   // Botões azul escuro
		style.Colors[ImGuiCol_ButtonHovered] = ImVec4(0.60f, 0.50f, 0.20f, 0.80f); // Hover dourado
		style.Colors[ImGuiCol_ButtonActive] = ImVec4(0.80f, 0.70f, 0.30f, 0.99f);  // Ativo dourado brilhante

		// Abas com tema azul e detalhes dourados
		style.Colors[ImGuiCol_Tab] = ImVec4(0.12f, 0.20f, 0.35f, 0.86f);		   // Abas inativas azul escuro
		style.Colors[ImGuiCol_TabHovered] = ImVec4(0.60f, 0.50f, 0.20f, 0.80f);   // Hover dourado
		style.Colors[ImGuiCol_TabActive] = ImVec4(0.20f, 0.35f, 0.60f, 0.99f);	   // Aba ativa azul
		style.Colors[ImGuiCol_TabUnfocused] = ImVec4(0.10f, 0.15f, 0.25f, 0.97f); // Abas sem foco azul muito escuro
		style.Colors[ImGuiCol_TabUnfocusedActive] = ImVec4(0.15f, 0.25f, 0.45f, 0.99f); // Aba ativa sem foco

		// Controles deslizantes e barras de progresso
		style.Colors[ImGuiCol_SliderGrab] = ImVec4(0.70f, 0.60f, 0.25f, 0.99f);		// Controle dourado
		style.Colors[ImGuiCol_SliderGrabActive] = ImVec4(0.90f, 0.80f, 0.40f, 0.99f); // Controle ativo dourado brilhante
		style.Colors[ImGuiCol_PlotLines] = ImVec4(0.61f, 0.61f, 0.61f, 0.99f);
		style.Colors[ImGuiCol_PlotLinesHovered] = ImVec4(1.00f, 0.43f, 0.35f, 0.99f);
		style.Colors[ImGuiCol_PlotHistogram] = ImVec4(0.90f, 0.70f, 0.00f, 0.99f);
		style.Colors[ImGuiCol_PlotHistogramHovered] = ImVec4(1.00f, 0.60f, 0.00f, 0.99f);

		// Elementos de seleção
		style.Colors[ImGuiCol_CheckMark] = ImVec4(0.90f, 0.80f, 0.40f, 0.99f);		// Marca de seleção dourada
		style.Colors[ImGuiCol_DragDropTarget] = ImVec4(1.00f, 1.00f, 0.00f, 0.90f); // Alvo de arrastar e soltar amarelo

		// Barras de rolagem
		style.Colors[ImGuiCol_ScrollbarBg] = ImVec4(0.08f, 0.12f, 0.18f, 0.53f);	   // Fundo da barra azul escuro
		style.Colors[ImGuiCol_ScrollbarGrab] = ImVec4(0.20f, 0.35f, 0.60f, 0.99f);	   // Controle azul
		style.Colors[ImGuiCol_ScrollbarGrabHovered] = ImVec4(0.25f, 0.45f, 0.75f, 0.99f); // Hover azul claro
		style.Colors[ImGuiCol_ScrollbarGrabActive] = ImVec4(0.30f, 0.55f, 0.90f, 0.99f);  // Ativo azul brilhante

		// Separadores
		style.Colors[ImGuiCol_Separator] = ImVec4(0.20f, 0.35f, 0.60f, 0.50f);		   // Separadores azuis
		style.Colors[ImGuiCol_SeparatorHovered] = ImVec4(0.60f, 0.50f, 0.20f, 0.78f); // Hover dourado
		style.Colors[ImGuiCol_SeparatorActive] = ImVec4(0.80f, 0.70f, 0.30f, 0.99f);  // Ativo dourado

		// Elementos de redimensionamento
		style.Colors[ImGuiCol_ResizeGrip] = ImVec4(0.20f, 0.35f, 0.60f, 0.25f);		   // Controle de redimensionamento azul
		style.Colors[ImGuiCol_ResizeGripHovered] = ImVec4(0.60f, 0.50f, 0.20f, 0.67f); // Hover dourado
		style.Colors[ImGuiCol_ResizeGripActive] = ImVec4(0.80f, 0.70f, 0.30f, 0.95f);  // Ativo dourado

		// Navegação
		style.Colors[ImGuiCol_NavHighlight] = ImVec4(0.26f, 0.59f, 0.98f, 0.99f);
		style.Colors[ImGuiCol_NavWindowingHighlight] = ImVec4(1.00f, 1.00f, 1.00f, 0.70f);
		style.Colors[ImGuiCol_NavWindowingDimBg] = ImVec4(0.80f, 0.80f, 0.80f, 0.20f);

		// Modal
		style.Colors[ImGuiCol_ModalWindowDimBg] = ImVec4(0.80f, 0.80f, 0.80f, 0.35f);
	}
	else // UITheme::OrangeGold
	{
		// Tema Laranja/Dourado (Secundário) - cores originais
		style.Colors[ImGuiCol_WindowBg] = ImVec4(0.10f, 0.10f, 0.12f, 0.95f);		// Fundo da janela mais escuro
		style.Colors[ImGuiCol_ChildBg] = ImVec4(0.15f, 0.15f, 0.18f, 0.95f);		// Fundo de elementos filhos
		style.Colors[ImGuiCol_PopupBg] = ImVec4(0.08f, 0.08f, 0.10f, 0.95f);		// Fundo de popups
		style.Colors[ImGuiCol_Border] = ImVec4(0.43f, 0.24f, 0.06f, 0.50f);		// Bordas com tom laranja
		style.Colors[ImGuiCol_BorderShadow] = ImVec4(0.00f, 0.00f, 0.00f, 0.00f);	// Sem sombra de borda
		style.Colors[ImGuiCol_FrameBg] = ImVec4(0.25f, 0.18f, 0.10f, 0.54f);		// Fundo de frames com tom laranja escuro
		style.Colors[ImGuiCol_FrameBgHovered] = ImVec4(0.38f, 0.25f, 0.13f, 0.40f); // Hover com tom laranja
		style.Colors[ImGuiCol_FrameBgActive] = ImVec4(0.55f, 0.35f, 0.15f, 0.67f);	// Ativo com tom laranja mais forte

		// Elementos de texto e títulos (Alpha 0.99f para compatibilidade AMD)
		style.Colors[ImGuiCol_Text] = ImVec4(0.90f, 0.90f, 0.90f, 0.99f);			// Texto normal branco
		style.Colors[ImGuiCol_TextDisabled] = ImVec4(0.60f, 0.60f, 0.60f, 0.99f);	// Texto desativado cinza
		style.Colors[ImGuiCol_TextSelectedBg] = ImVec4(0.80f, 0.50f, 0.20f, 0.35f); // Fundo de texto selecionado laranja
		// Cores do header da janela - ajustadas para combinar com o tema laranja
		style.Colors[ImGuiCol_TitleBg] = ImVec4(0.45f, 0.25f, 0.08f, 0.9f);		  // Fundo do título laranja escuro
		style.Colors[ImGuiCol_TitleBgActive] = ImVec4(0.70f, 0.40f, 0.10f, 0.99f);	  // Fundo do título ativo laranja
		style.Colors[ImGuiCol_TitleBgCollapsed] = ImVec4(0.45f, 0.25f, 0.08f, 0.75f); // Fundo do título recolhido

		// Elementos interativos (Alpha 0.99f para compatibilidade AMD)
		style.Colors[ImGuiCol_Header] = ImVec4(0.70f, 0.40f, 0.10f, 0.70f);		   // Cabeçalhos laranja
		style.Colors[ImGuiCol_HeaderHovered] = ImVec4(0.80f, 0.50f, 0.15f, 0.80f); // Hover de cabeçalhos laranja mais claro
		style.Colors[ImGuiCol_HeaderActive] = ImVec4(0.90f, 0.60f, 0.20f, 0.99f);  // Cabeçalhos ativos laranja brilhante

		// Botões com tema laranja/dourado (Alpha 0.99f para compatibilidade AMD)
		style.Colors[ImGuiCol_Button] = ImVec4(0.70f, 0.40f, 0.10f, 0.70f);		   // Botões laranja
		style.Colors[ImGuiCol_ButtonHovered] = ImVec4(0.80f, 0.50f, 0.15f, 0.80f); // Hover laranja mais claro
		style.Colors[ImGuiCol_ButtonActive] = ImVec4(0.90f, 0.60f, 0.20f, 0.99f);  // Ativo laranja brilhante

		// Abas com tema laranja (Alpha 0.99f para compatibilidade AMD)
		style.Colors[ImGuiCol_Tab] = ImVec4(0.58f, 0.35f, 0.14f, 0.86f);		   // Abas inativas laranja escuro
		style.Colors[ImGuiCol_TabHovered] = ImVec4(0.80f, 0.50f, 0.15f, 0.80f);   // Hover laranja
		style.Colors[ImGuiCol_TabActive] = ImVec4(0.70f, 0.40f, 0.10f, 0.99f);	   // Aba ativa laranja
		style.Colors[ImGuiCol_TabUnfocused] = ImVec4(0.45f, 0.25f, 0.08f, 0.97f); // Abas sem foco laranja escuro
		style.Colors[ImGuiCol_TabUnfocusedActive] = ImVec4(0.60f, 0.35f, 0.12f, 0.99f); // Aba ativa sem foco

		// Controles deslizantes e barras de progresso (Alpha 0.99f para compatibilidade AMD)
		style.Colors[ImGuiCol_SliderGrab] = ImVec4(0.90f, 0.60f, 0.20f, 0.99f);		// Controle laranja
		style.Colors[ImGuiCol_SliderGrabActive] = ImVec4(1.00f, 0.70f, 0.30f, 0.99f); // Controle ativo laranja brilhante
		style.Colors[ImGuiCol_PlotLines] = ImVec4(0.61f, 0.61f, 0.61f, 0.99f);
		style.Colors[ImGuiCol_PlotLinesHovered] = ImVec4(1.00f, 0.43f, 0.35f, 0.99f);
		style.Colors[ImGuiCol_PlotHistogram] = ImVec4(0.90f, 0.70f, 0.00f, 0.99f);
		style.Colors[ImGuiCol_PlotHistogramHovered] = ImVec4(1.00f, 0.60f, 0.00f, 0.99f);

		// Elementos de seleção (Alpha 0.99f para compatibilidade AMD)
		style.Colors[ImGuiCol_CheckMark] = ImVec4(0.90f, 0.60f, 0.20f, 0.99f);		// Marca de seleção laranja
		style.Colors[ImGuiCol_DragDropTarget] = ImVec4(1.00f, 1.00f, 0.00f, 0.90f); // Alvo de arrastar e soltar amarelo

		// Barras de rolagem (Alpha 0.99f para compatibilidade AMD)
		style.Colors[ImGuiCol_ScrollbarBg] = ImVec4(0.10f, 0.10f, 0.12f, 0.53f);	   // Fundo da barra escuro
		style.Colors[ImGuiCol_ScrollbarGrab] = ImVec4(0.70f, 0.40f, 0.10f, 0.99f);	   // Controle laranja
		style.Colors[ImGuiCol_ScrollbarGrabHovered] = ImVec4(0.80f, 0.50f, 0.15f, 0.99f); // Hover laranja claro
		style.Colors[ImGuiCol_ScrollbarGrabActive] = ImVec4(0.90f, 0.60f, 0.20f, 0.99f);  // Ativo laranja brilhante

		// Separadores (Alpha 0.99f para compatibilidade AMD)
		style.Colors[ImGuiCol_Separator] = ImVec4(0.43f, 0.24f, 0.06f, 0.50f);		   // Separadores laranja
		style.Colors[ImGuiCol_SeparatorHovered] = ImVec4(0.80f, 0.50f, 0.15f, 0.78f); // Hover laranja claro
		style.Colors[ImGuiCol_SeparatorActive] = ImVec4(0.90f, 0.60f, 0.20f, 0.99f);  // Ativo laranja brilhante

		// Elementos de redimensionamento (Alpha 0.99f para compatibilidade AMD)
		style.Colors[ImGuiCol_ResizeGrip] = ImVec4(0.70f, 0.40f, 0.10f, 0.25f);		   // Controle de redimensionamento laranja
		style.Colors[ImGuiCol_ResizeGripHovered] = ImVec4(0.80f, 0.50f, 0.15f, 0.67f); // Hover laranja claro
		style.Colors[ImGuiCol_ResizeGripActive] = ImVec4(0.90f, 0.60f, 0.20f, 0.95f);  // Ativo laranja brilhante

		// Navegação (Alpha 0.99f para compatibilidade AMD)
		style.Colors[ImGuiCol_NavHighlight] = ImVec4(0.26f, 0.59f, 0.98f, 0.99f);
		style.Colors[ImGuiCol_NavWindowingHighlight] = ImVec4(1.00f, 1.00f, 1.00f, 0.70f);
		style.Colors[ImGuiCol_NavWindowingDimBg] = ImVec4(0.80f, 0.80f, 0.80f, 0.20f);

		// Modal (Alpha 0.99f para compatibilidade AMD)
		style.Colors[ImGuiCol_ModalWindowDimBg] = ImVec4(0.80f, 0.80f, 0.80f, 0.35f);
	}
}

// Função para carregar automaticamente a configuração padrão ou o último perfil utilizado
void LoadDefaultConfig()
{
	if (!configLoaded)
	{
		EnsureConfigDirectoryExists();

		// Tentar carregar o último perfil utilizado
		char lastProfileName[256];
		if (LoadLastUsedProfile(lastProfileName, sizeof(lastProfileName)))
		{
			// Se o último perfil foi carregado com sucesso, usá-lo
			LoadConfig(lastProfileName);
		}
		else
		{
			// Caso contrário, verificar se o arquivo padrão existe
			char fullPath[512];
			sprintf_s(fullPath, "%s/%s", CONFIG_DIR, DEFAULT_CONFIG);

			std::ifstream file(fullPath);
			if (file.is_open())
			{
				file.close();
				LoadConfig(DEFAULT_CONFIG);
			}
			else
			{
				// Se não existir, criar um novo arquivo com as configurações atuais
				SaveConfig(DEFAULT_CONFIG);
			}
		}

		// Aplicar o tema após carregar a configuração
		ApplyTheme(static_cast<UITheme>(mods::currentTheme));

		configLoaded = true;
	}
}

//=========================================================================================================================//

typedef HRESULT(APIENTRY *Present12)(IDXGISwapChain *pSwapChain, UINT SyncInterval, UINT Flags);
Present12 oPresent = NULL;

typedef void(APIENTRY *DrawInstanced)(ID3D12GraphicsCommandList *dCommandList, UINT VertexCountPerInstance, UINT InstanceCount, UINT StartVertexLocation, UINT StartInstanceLocation);
DrawInstanced oDrawInstanced = NULL;

typedef void(APIENTRY *DrawIndexedInstanced)(ID3D12GraphicsCommandList *dCommandList, UINT IndexCountPerInstance, UINT InstanceCount, UINT StartIndexLocation, INT BaseVertexLocation, UINT StartInstanceLocation);
DrawIndexedInstanced oDrawIndexedInstanced = NULL;

typedef void(APIENTRY *ExecuteCommandLists)(ID3D12CommandQueue *queue, UINT NumCommandLists, ID3D12CommandList *ppCommandLists);
ExecuteCommandLists oExecuteCommandLists = NULL;

//=========================================================================================================================//

bool ShowMenu = false;
bool ImGui_Initialised = false;

namespace Process
{
	DWORD ID;
	HANDLE Handle;
	HWND Hwnd;
	HMODULE Module;
	WNDPROC WndProc;
	int WindowWidth;
	int WindowHeight;
	LPCSTR Title;
	LPCSTR ClassName;
	LPCSTR Path;
}

namespace DirectX12Interface
{
	ID3D12Device *Device = nullptr;
	ID3D12DescriptorHeap *DescriptorHeapBackBuffers;
	ID3D12DescriptorHeap *DescriptorHeapImGuiRender;
	ID3D12GraphicsCommandList *CommandList;
	ID3D12CommandQueue *CommandQueue;

	struct _FrameContext
	{
		ID3D12CommandAllocator *CommandAllocator;
		ID3D12Resource *Resource;
		D3D12_CPU_DESCRIPTOR_HANDLE DescriptorHandle;
	};

	uintx_t BuffersCounts = -1;
	_FrameContext *FrameContext;
	bool ResourcesInitialized = false;
	bool ResizeInProgress = false;

	// Função para limpar os recursos de renderização (usado durante redimensionamento)
	void CleanupRenderTarget()
	{
		// Esperar que todos os comandos pendentes sejam concluídos
		if (CommandQueue && CommandList)
		{
			CommandList->Close();
			CommandQueue->ExecuteCommandLists(1, reinterpret_cast<ID3D12CommandList *const *>(&CommandList));
		}

		// Liberar os recursos de backbuffer
		for (UINT i = 0; i < BuffersCounts; i++)
		{
			if (FrameContext[i].Resource)
			{
				FrameContext[i].Resource->Release();
				FrameContext[i].Resource = NULL;
			}
		}

		ResourcesInitialized = false;
	}

	// Função para criar os recursos de renderização
	void CreateRenderTarget(IDXGISwapChain3 *pSwapChain)
	{
		// Verificar se os recursos já foram inicializados
		if (ResourcesInitialized)
			return;

		// Criar os recursos de backbuffer
		const auto RTVDescriptorSize = Device->GetDescriptorHandleIncrementSize(D3D12_DESCRIPTOR_HEAP_TYPE_RTV);
		D3D12_CPU_DESCRIPTOR_HANDLE RTVHandle = DescriptorHeapBackBuffers->GetCPUDescriptorHandleForHeapStart();

		for (UINT i = 0; i < BuffersCounts; i++)
		{
			ID3D12Resource *pBackBuffer = nullptr;
			FrameContext[i].DescriptorHandle = RTVHandle;
			pSwapChain->GetBuffer(i, IID_PPV_ARGS(&pBackBuffer));
			Device->CreateRenderTargetView(pBackBuffer, nullptr, RTVHandle);
			FrameContext[i].Resource = pBackBuffer;
			RTVHandle.ptr += RTVDescriptorSize;
		}

		ResourcesInitialized = true;
		ResizeInProgress = false;
	}
}

//=========================================================================================================================//
DWORD WINAPI CreateConsole(LPVOID lpParameter)
{
	if (!AllocConsole())
	{
		return 1;
	}

	FILE *fDummy;
	freopen_s(&fDummy, "CONOUT$", "w", stdout);
	freopen_s(&fDummy, "CONOUT$", "w", stderr);
	freopen_s(&fDummy, "CONIN$", "r", stdin);
	std::cout.clear();
	std::clog.clear();
	std::cerr.clear();
	std::cin.clear();

	HANDLE hConOut = CreateFile(("CONOUT$"), GENERIC_READ | GENERIC_WRITE, FILE_SHARE_READ | FILE_SHARE_WRITE, NULL, OPEN_EXISTING, FILE_ATTRIBUTE_NORMAL, NULL);
	HANDLE hConIn = CreateFile(("CONIN$"), GENERIC_READ | GENERIC_WRITE, FILE_SHARE_READ | FILE_SHARE_WRITE, NULL, OPEN_EXISTING, FILE_ATTRIBUTE_NORMAL, NULL);
	SetStdHandle(STD_OUTPUT_HANDLE, hConOut);
	SetStdHandle(STD_ERROR_HANDLE, hConOut);
	SetStdHandle(STD_INPUT_HANDLE, hConIn);
	std::wcout.clear();
	std::wclog.clear();
	std::wcerr.clear();
	std::wcin.clear();
	return 0;
}

//=========================================================================================================================//

extern IMGUI_IMPL_API LRESULT ImGui_ImplWin32_WndProcHandler(HWND hWnd, UINT msg, WPARAM wParam, LPARAM lParam);
LRESULT APIENTRY WndProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam)
{
	// Processar eventos de redimensionamento
	if (ImGui_Initialised && uMsg == WM_SIZE)
	{
		if (wParam != SIZE_MINIMIZED)
		{
			// Marcar que um redimensionamento está em andamento
			DirectX12Interface::ResizeInProgress = true;

			// Limpar os recursos existentes antes do redimensionamento
			DirectX12Interface::CleanupRenderTarget();

			// Permitir que o jogo redimensione o SwapChain
			// A recriação dos recursos será feita na próxima chamada a hkPresent
		}
	}

	// Processar eventos de ImGui se o menu estiver visível
	if (ShowMenu)
	{
		ImGui_ImplWin32_WndProcHandler(hwnd, uMsg, wParam, lParam);
		return true;
	}

	// Processar eventos de zoom mesmo quando o menu não está visível
	if (mods::enableUIZoom && uMsg == WM_MOUSEWHEEL && (GetKeyState(VK_CONTROL) & 0x8000))
	{
		// Obter a direção da rolagem da roda do mouse
		float wheelDelta = (float)GET_WHEEL_DELTA_WPARAM(wParam) / (float)WHEEL_DELTA;

		// Ajustar o fator de zoom
		mods::uiZoomFactor += wheelDelta * mods::uiZoomStep;

		// Limitar o zoom aos valores mínimo e máximo
		mods::uiZoomFactor = mods::Clamp(mods::uiZoomFactor, mods::uiZoomMin, mods::uiZoomMax);

		return true;
	}

	return CallWindowProc(Process::WndProc, hwnd, uMsg, wParam, lParam);
}

//=========================================================================================================================//

HRESULT APIENTRY hkPresent(IDXGISwapChain3 *pSwapChain, UINT SyncInterval, UINT Flags)
{
	// Validação básica - log apenas falhas críticas
	static bool lastValidationState = true;
	bool currentValidationState = IsValidPtr(pSwapChain) && IsValidPtr(DirectX12Interface::CommandQueue) && ImGui_Initialised;

	// Log apenas quando estado muda (falha ou recuperação)
	if (currentValidationState != lastValidationState)
	{
		if (currentValidationState)
		{
			SafetySystem::RegisterSuccess();
		}
		else
		{
			SafetySystem::LogError("hkPresent", "Componentes críticos inválidos detectados");
		}
		lastValidationState = currentValidationState;
	}

	if (!IsValidPtr(pSwapChain))
	{
		SafetySystem::LogError("hkPresent", "pSwapChain inválido - retornando oPresent");
		return oPresent(pSwapChain, SyncInterval, Flags);
	}
	if (!IsValidPtr(DirectX12Interface::CommandQueue))
	{
		SafetySystem::LogError("hkPresent", "CommandQueue inválido - retornando oPresent");
		return oPresent(pSwapChain, SyncInterval, Flags);
	}

	if (!ImGui_Initialised)
	{
		if (SUCCEEDED(pSwapChain->GetDevice(__uuidof(ID3D12Device), (void **)&DirectX12Interface::Device)))
		{
			if (!IsValidPtr(DirectX12Interface::Device))
			{
				return oPresent(pSwapChain, SyncInterval, Flags);
			}
			ImGui::CreateContext();

			ImGuiIO &io = ImGui::GetIO();
			(void)io;
			ImGui::GetIO().WantCaptureMouse || ImGui::GetIO().WantTextInput || ImGui::GetIO().WantCaptureKeyboard;
			io.ConfigFlags |= ImGuiConfigFlags_NavEnableKeyboard;
			io.FontAllowUserScaling = mods::enableUIZoom; // Habilitar zoom com CTRL+Mouse Wheel

			// Configurar o arquivo .ini do ImGui para salvar as configurações da UI
			char iniPath[512];
			sprintf_s(iniPath, "%s/imgui.ini", CONFIG_DIR);
			io.IniFilename = _strdup(iniPath);

			// Inicializar o sistema de idiomas
			LanguageSystem::Get().Initialize();
			LanguageSystem::Get().SetLanguage(static_cast<Language>(mods::currentLanguage));

			DXGI_SWAP_CHAIN_DESC Desc;
			pSwapChain->GetDesc(&Desc);
			Desc.Flags = DXGI_SWAP_CHAIN_FLAG_ALLOW_MODE_SWITCH;
			Desc.OutputWindow = Process::Hwnd;
			Desc.Windowed = ((GetWindowLongPtr(Process::Hwnd, GWL_STYLE) & WS_POPUP) != 0) ? false : true;

			DirectX12Interface::BuffersCounts = Desc.BufferCount;
			DirectX12Interface::FrameContext = new DirectX12Interface::_FrameContext[DirectX12Interface::BuffersCounts];

			D3D12_DESCRIPTOR_HEAP_DESC DescriptorImGuiRender = {};
			DescriptorImGuiRender.Type = D3D12_DESCRIPTOR_HEAP_TYPE_CBV_SRV_UAV;
			DescriptorImGuiRender.NumDescriptors = DirectX12Interface::BuffersCounts;
			DescriptorImGuiRender.Flags = D3D12_DESCRIPTOR_HEAP_FLAG_SHADER_VISIBLE;

			if (DirectX12Interface::Device->CreateDescriptorHeap(&DescriptorImGuiRender, IID_PPV_ARGS(&DirectX12Interface::DescriptorHeapImGuiRender)) != S_OK)
				return oPresent(pSwapChain, SyncInterval, Flags);

			ID3D12CommandAllocator *Allocator;
			if (DirectX12Interface::Device->CreateCommandAllocator(D3D12_COMMAND_LIST_TYPE_DIRECT, IID_PPV_ARGS(&Allocator)) != S_OK)
				return oPresent(pSwapChain, SyncInterval, Flags);

			for (size_t i = 0; i < DirectX12Interface::BuffersCounts; i++)
			{
				DirectX12Interface::FrameContext[i].CommandAllocator = Allocator;
			}

			if (DirectX12Interface::Device->CreateCommandList(0, D3D12_COMMAND_LIST_TYPE_DIRECT, Allocator, NULL, IID_PPV_ARGS(&DirectX12Interface::CommandList)) != S_OK ||
				DirectX12Interface::CommandList->Close() != S_OK)
				return oPresent(pSwapChain, SyncInterval, Flags);

			D3D12_DESCRIPTOR_HEAP_DESC DescriptorBackBuffers;
			DescriptorBackBuffers.Type = D3D12_DESCRIPTOR_HEAP_TYPE_RTV;
			DescriptorBackBuffers.NumDescriptors = DirectX12Interface::BuffersCounts;
			DescriptorBackBuffers.Flags = D3D12_DESCRIPTOR_HEAP_FLAG_NONE;
			DescriptorBackBuffers.NodeMask = 1;

			if (DirectX12Interface::Device->CreateDescriptorHeap(&DescriptorBackBuffers, IID_PPV_ARGS(&DirectX12Interface::DescriptorHeapBackBuffers)) != S_OK)
				return oPresent(pSwapChain, SyncInterval, Flags);

			// Criar os recursos de renderização usando nossa função
			DirectX12Interface::CreateRenderTarget(pSwapChain);

			ImGui_ImplWin32_Init(Process::Hwnd);
			//---------------------------------------------------------------------
			// 		🔤	Configuração de Fontes (ANTES da inicialização DX12)
			//---------------------------------------------------------------------

			// Limpar fontes existentes para garantir ordem correta
			io.Fonts->Clear();

			// Carregar fonte Montserrat Regular como PRIMEIRA fonte (será a padrão)
			ImFontConfig montserrat_config;
			montserrat_config.FontDataOwnedByAtlas = false; // IMPORTANTE: Não liberar os dados da fonte
			montserrat_config.SizePixels = 16.0f;			// Tamanho da fonte em pixels
			montserrat_config.GlyphExtraSpacing.x = 1.0f;	// Espaçamento extra para melhor legibilidade
			montserrat_config.RasterizerMultiply = 1.3f;	// Tornar a fonte um pouco mais negrito
			montserrat_config.OversampleH = 3;				// Melhorar a suavização horizontal
			montserrat_config.OversampleV = 3;				// Melhorar a suavização vertical

			// Adicionar a fonte Montserrat ao atlas de fontes (primeira fonte = padrão)
			ImFont* montserrat_font = io.Fonts->AddFontFromMemoryTTF(
				(void*)montserrat_regular,
				montserrat_regular_size,
				16.0f,
				&montserrat_config
			);

			// Carregar fonte Montserrat Bold para títulos (segunda fonte)
			ImFontConfig montserrat_bold_config;
			montserrat_bold_config.FontDataOwnedByAtlas = false; // IMPORTANTE: Não liberar os dados da fonte
			montserrat_bold_config.SizePixels = 18.0f;			 // Tamanho maior para títulos
			montserrat_bold_config.GlyphExtraSpacing.x = 1.0f;	 // Espaçamento extra para melhor legibilidade
			montserrat_bold_config.RasterizerMultiply = 1.4f;	 // Tornar a fonte mais negrito
			montserrat_bold_config.OversampleH = 3;				 // Melhorar a suavização horizontal
			montserrat_bold_config.OversampleV = 3;				 // Melhorar a suavização vertical

			ImFont* montserrat_bold_font = io.Fonts->AddFontFromMemoryTTF(
				(void*)montserrat_bold,
				montserrat_bold_size,
				18.0f,
				&montserrat_bold_config
			);

			// Carregar fonte padrão como fallback (terceira fonte)
			ImFontConfig default_config;
			default_config.SizePixels = 18.0f;		   // Tamanho maior da fonte
			default_config.GlyphExtraSpacing.x = 1.0f; // Espaçamento extra para melhor legibilidade
			default_config.RasterizerMultiply = 1.5f;  // Tornar a fonte mais negrito
			default_config.OversampleH = 3;			   // Melhorar a suavização horizontal
			default_config.OversampleV = 3;			   // Melhorar a suavização vertical
			io.Fonts->AddFontDefault(&default_config);

			// Definir Montserrat como fonte padrão ANTES de construir o atlas
			if (montserrat_font != nullptr)
			{
				io.FontDefault = montserrat_font;
			}

			// Construir o atlas de fontes (DEVE ser chamado após definir FontDefault)
			io.Fonts->Build();

			ImGui_ImplDX12_Init(DirectX12Interface::Device, DirectX12Interface::BuffersCounts, DXGI_FORMAT_R8G8B8A8_UNORM, DirectX12Interface::DescriptorHeapImGuiRender, DirectX12Interface::DescriptorHeapImGuiRender->GetCPUDescriptorHandleForHeapStart(), DirectX12Interface::DescriptorHeapImGuiRender->GetGPUDescriptorHandleForHeapStart());
			ImGui_ImplDX12_CreateDeviceObjects();
			ImGui::GetIO().ImeWindowHandle = Process::Hwnd;

			// Configurar estilo da interface
			io.FontGlobalScale = 1.3f; // Aumentar escala da interface
			ImGuiStyle &style = ImGui::GetStyle();
			style.WindowRounding = 12.0f;			 // Cantos arredondados nas janelas
			style.ChildRounding = 8.0f;				 // Cantos arredondados nos elementos filhos
			style.FrameRounding = 6.0f;				 // Cantos arredondados nos frames
			style.TabRounding = 8.0f;				 // Cantos arredondados nas abas
			style.GrabRounding = 6.0f;				 // Cantos arredondados nos controles deslizantes
			style.ScrollbarRounding = 6.0f;			 // Cantos arredondados nas barras de rolagem
			style.FramePadding = ImVec2(8.0f, 6.0f); // Mais espaço para elementos
			style.ItemSpacing = ImVec2(10.0f, 8.0f); // Mais espaço entre itens

			// Melhorar a aparência das barras de rolagem
			style.ScrollbarSize = 16.0f;	// Barras de rolagem mais largas
			style.ScrollbarRounding = 8.0f; // Cantos mais arredondados
			style.GrabMinSize = 12.0f;		// Tamanho mínimo do controle deslizante

			// Aplicar o tema padrão (será atualizado quando a configuração for carregada)
			ApplyTheme(UITheme::DarkBlueGold);

			// Carregar a configuração padrão
			LoadDefaultConfig();
			Process::WndProc = (WNDPROC)SetWindowLongPtr(Process::Hwnd, GWLP_WNDPROC, (__int3264)(LONG_PTR)WndProc);
		}
		ImGui_Initialised = true;
	}

	if (!IsValidPtr(DirectX12Interface::CommandQueue))
	{
		return oPresent(pSwapChain, SyncInterval, Flags);
	}

	// Verificar se os recursos precisam ser recriados após um redimensionamento
	if (DirectX12Interface::ResizeInProgress || !DirectX12Interface::ResourcesInitialized)
	{
		// Recriar os recursos de renderização
		DirectX12Interface::CreateRenderTarget(pSwapChain);
	}

	// Sistema de verificação de tecla de menu mais robusto
	static bool lastInsertState = false;
	static DWORD lastInsertTime = 0;
	static int insertPressCount = 0;

	bool currentInsertState = (GetAsyncKeyState(VK_INSERT) & 0x8000) != 0;
	DWORD currentInsertTime = GetTickCount();

	// Detectar transição de não pressionada para pressionada
	if (currentInsertState && !lastInsertState) {
		insertPressCount++;
		lastInsertTime = currentInsertTime;
	}

	// Se detectamos um pressionamento e passou tempo suficiente para debounce
	if (insertPressCount > 0 && (currentInsertTime - lastInsertTime) > 50) { // 50ms debounce
		ShowMenu = !ShowMenu;
		insertPressCount = 0; // Reset contador
		printf("Menu toggled: %s\n", ShowMenu ? "Open" : "Closed");
	}

	// Reset contador se passou muito tempo sem ação
	if ((currentInsertTime - lastInsertTime) > 500) { // 500ms timeout
		insertPressCount = 0;
	}

	lastInsertState = currentInsertState;
	ImGui_ImplDX12_NewFrame();
	ImGui_ImplWin32_NewFrame();
	ImGui::NewFrame();
	ImGui::GetIO().MouseDrawCursor = ShowMenu;
	ImGui::GetIO().FontGlobalScale = mods::uiZoomFactor; // Aplicar o fator de zoom global

	// Atualizar a resolução da tela dinamicamente
	ImVec2 displaySize = ImGui::GetIO().DisplaySize;
	if (displaySize.x > 0 && displaySize.y > 0)
	{
		Variables::UpdateScreenResolution(displaySize.x, displaySize.y);
	}

	// Obter World e Engine com verificação de segurança + Sistema de Retry

	//---------------------------------------------------------------------
	// 		🔄	Sistema de Retry com Backoff para Falhas Temporárias
	//---------------------------------------------------------------------
	static int worldRetryCount = 0;
	static int engineRetryCount = 0;
	static auto lastWorldRetryTime = std::chrono::steady_clock::now();
	static auto lastEngineRetryTime = std::chrono::steady_clock::now();
	const int MAX_RETRIES = 3;
	const int RETRY_INTERVAL_MS = 100; // 100ms entre tentativas

	// Verificação segura para obter World - CORRIGIDA
	try
	{
		if (!IsValidPtr(UWorld::StaticClass()))
		{
			SafetySystem::LogError("MainLoop", "UWorld::StaticClass() é inválido - SDK pode não estar carregado");
			Variables::World = nullptr;
		}
		else
		{
			// 🚀 CORREÇÃO CRÍTICA: Validar ANTES de atribuir + Validação robusta + Sistema de Retry
			UWorld* tempWorld = UWorld::GetWorld();
			if (!IsValidObjectPtr(tempWorld))
			{
				// 🔄 Sistema de Retry para falhas temporárias
				auto now = std::chrono::steady_clock::now();
				auto timeSinceLastRetry = std::chrono::duration_cast<std::chrono::milliseconds>(now - lastWorldRetryTime).count();

				if (worldRetryCount < MAX_RETRIES && timeSinceLastRetry > RETRY_INTERVAL_MS)
				{
					worldRetryCount++;
					lastWorldRetryTime = now;
					SafetySystem::LogError("MainLoop", ("UWorld inválido - tentativa " + std::to_string(worldRetryCount) + "/" + std::to_string(MAX_RETRIES)).c_str());
					Variables::World = nullptr;
					goto skip_world_processing; // Pula para o final, tenta novamente no próximo frame
				}
				else if (worldRetryCount >= MAX_RETRIES)
				{
					SafetySystem::LogError("MainLoop", "UWorld::GetWorld() falhou após máximo de tentativas - assumindo falha permanente");
					Variables::World = nullptr;
					worldRetryCount = 0; // Reset para próxima sequência
				}
				else
				{
					// Ainda dentro do intervalo de retry, não fazer nada
					Variables::World = nullptr;
				}
			}
			else if (!IsValidForCurrentMap(tempWorld))
			{
				// 🔄 Sistema de Retry para validação de mapa
				auto now = std::chrono::steady_clock::now();
				auto timeSinceLastRetry = std::chrono::duration_cast<std::chrono::milliseconds>(now - lastWorldRetryTime).count();

				if (worldRetryCount < MAX_RETRIES && timeSinceLastRetry > RETRY_INTERVAL_MS)
				{
					worldRetryCount++;
					lastWorldRetryTime = now;
					SafetySystem::LogError("MainLoop", ("UWorld falhou na validação do mapa - tentativa " + std::to_string(worldRetryCount) + "/" + std::to_string(MAX_RETRIES)).c_str());
					Variables::World = nullptr;
					goto skip_world_processing; // Pula para o final, tenta novamente no próximo frame
				}
				else if (worldRetryCount >= MAX_RETRIES)
				{
					SafetySystem::LogError("MainLoop", "UWorld falhou na validação do mapa após máximo de tentativas");
					Variables::World = nullptr;
					worldRetryCount = 0; // Reset para próxima sequência
				}
				else
				{
					Variables::World = nullptr;
				}
			}
			else
			{
				Variables::World = tempWorld; // ✅ Só atribui se válido E compatível com o mapa
				worldRetryCount = 0; // Reset contador em caso de sucesso
				SafetySystem::RegisterSuccess(); // Sistema funcionando
			}
		}
	}
	catch (...)
	{
		SafetySystem::LogError("MainLoop", "EXCEÇÃO ao obter UWorld");
		Variables::World = nullptr;
	}

	// Verificação segura para obter Engine - CORRIGIDA
	try
	{
		if (!IsValidPtr(UEngine::StaticClass()))
		{
			SafetySystem::LogError("MainLoop", "UEngine::StaticClass() é inválido - SDK pode não estar carregado");
			Variables::Engine = nullptr;
		}
		else
		{
			// 🚀 CORREÇÃO CRÍTICA: Validar ANTES de atribuir + Sistema de Retry
			Variables::Engine = UEngine::GetEngine();
			if (!IsValidObjectPtr(Variables::Engine))
			{
				// 🔄 Sistema de Retry para falhas temporárias do Engine
				auto now = std::chrono::steady_clock::now();
				auto timeSinceLastRetry = std::chrono::duration_cast<std::chrono::milliseconds>(now - lastEngineRetryTime).count();

				if (engineRetryCount < MAX_RETRIES && timeSinceLastRetry > RETRY_INTERVAL_MS)
				{
					engineRetryCount++;
					lastEngineRetryTime = now;
					SafetySystem::LogError("MainLoop", ("UEngine inválido - tentativa " + std::to_string(engineRetryCount) + "/" + std::to_string(MAX_RETRIES)).c_str());
					Variables::Engine = nullptr;
					goto skip_world_processing; // Pula para o final, tenta novamente no próximo frame
				}
				else if (engineRetryCount >= MAX_RETRIES)
				{
					SafetySystem::LogError("MainLoop", "UEngine::GetEngine() falhou após máximo de tentativas - assumindo falha permanente");
					Variables::Engine = nullptr;
					engineRetryCount = 0; // Reset para próxima sequência
				}
				else
				{
					// Ainda dentro do intervalo de retry, não fazer nada
					Variables::Engine = nullptr;
				}
			}
			else
			{
				// ✅ Ponteiro do Engine obtido com sucesso
				engineRetryCount = 0; // Reset contador em caso de sucesso
				SafetySystem::RegisterSuccess(); // Sistema funcionando
			}
		}
	}
	catch (...)
	{
		SafetySystem::LogError("MainLoop", "EXCEÇÃO ao obter UEngine");
		Variables::Engine = nullptr;
	}

	// Initialize all key definitions
	static bool keysInitialized = false;
	if (!keysInitialized && IsValidObjectPtr(UKismetStringLibrary::StaticClass()))
	{
		Keys::Insert = FKey{UKismetStringLibrary::Conv_StringToName(FString(L"Insert")), 0};

	// Initialize gamepad buttons
	Keys::GamepadButtonNone = FKey{UKismetStringLibrary::Conv_StringToName(FString(L"None")), 0}; // Tecla "None" para representar ausência de tecla
	Keys::GamepadButtonA = FKey{UKismetStringLibrary::Conv_StringToName(FString(L"Gamepad_FaceButton_Bottom")), 0};
	Keys::GamepadButtonB = FKey{UKismetStringLibrary::Conv_StringToName(FString(L"Gamepad_FaceButton_Right")), 0};
	Keys::GamepadButtonX = FKey{UKismetStringLibrary::Conv_StringToName(FString(L"Gamepad_FaceButton_Left")), 0};
	Keys::GamepadButtonY = FKey{UKismetStringLibrary::Conv_StringToName(FString(L"Gamepad_FaceButton_Top")), 0};
	Keys::GamepadDPadUp = FKey{UKismetStringLibrary::Conv_StringToName(FString(L"Gamepad_DPad_Up")), 0};
	Keys::GamepadDPadDown = FKey{UKismetStringLibrary::Conv_StringToName(FString(L"Gamepad_DPad_Down")), 0};
	Keys::GamepadDPadLeft = FKey{UKismetStringLibrary::Conv_StringToName(FString(L"Gamepad_DPad_Left")), 0};
	Keys::GamepadDPadRight = FKey{UKismetStringLibrary::Conv_StringToName(FString(L"Gamepad_DPad_Right")), 0};
	Keys::GamepadLeftShoulder = FKey{UKismetStringLibrary::Conv_StringToName(FString(L"Gamepad_LeftShoulder")), 0};
	Keys::GamepadRightShoulder = FKey{UKismetStringLibrary::Conv_StringToName(FString(L"Gamepad_RightShoulder")), 0};
	Keys::GamepadLeftTrigger = FKey{UKismetStringLibrary::Conv_StringToName(FString(L"Gamepad_LeftTrigger")), 0};
	Keys::GamepadRightTrigger = FKey{UKismetStringLibrary::Conv_StringToName(FString(L"Gamepad_RightTrigger")), 0};
	Keys::GamepadLeftThumb = FKey{UKismetStringLibrary::Conv_StringToName(FString(L"Gamepad_LeftThumbstick")), 0};
		Keys::GamepadRightThumb = FKey{UKismetStringLibrary::Conv_StringToName(FString(L"Gamepad_RightThumbstick")), 0};

		keysInitialized = true; // Marcar como inicializado para não repetir
	}

	// Set the current keys based on mod settings
	// Inicializar as teclas configuráveis com tratamento de erros
	try {
		// Verificar se o índice do botão do gamepad é válido
		if (mods::gamepad_aimbot_button < 0 || mods::gamepad_aimbot_button >= 14) {
			printf("Invalid gamepad aimbot button index: %d, resetting to 0\n", mods::gamepad_aimbot_button);
			mods::gamepad_aimbot_button = 0;
		}

		if (mods::gamepad_bullet_tp_button < 0 || mods::gamepad_bullet_tp_button >= 14) {
			printf("Invalid gamepad bullet TP button index: %d, resetting to 0\n", mods::gamepad_bullet_tp_button);
			mods::gamepad_bullet_tp_button = 0;
		}

		// Obter as teclas com tratamento de erros
		SDK::FKey* aimbotGamepadButton = Keys::GetGamepadButtonByIndex(mods::gamepad_aimbot_button);
		SDK::FKey* bulletTPGamepadButton = Keys::GetGamepadButtonByIndex(mods::gamepad_bullet_tp_button);

		// Verificar se os ponteiros são válidos usando safe_memory_check
		if (!IsValidSDKPtr(aimbotGamepadButton)) {
			printf("Invalid aimbot gamepad button pointer, using default\n");
			aimbotGamepadButton = Keys::GetGamepadButtonByIndex(0);
		}

		if (!IsValidSDKPtr(bulletTPGamepadButton)) {
			printf("Invalid bullet TP gamepad button pointer, using default\n");
			bulletTPGamepadButton = Keys::GetGamepadButtonByIndex(0);
		}

		// Atualizar as teclas configuráveis
		Keys::CurrentAimbotKey = Keys::GetFKeyFromKeyCode(mods::aimbot_key);
		Keys::CurrentBulletTPKey = Keys::GetFKeyFromKeyCode(mods::bullet_tp_key);
		Keys::CurrentTeamTargetKey = Keys::GetFKeyFromKeyCode(mods::teamTargetKey);
	} catch (const std::exception& e) {
		printf("Exception when initializing keys: %s\n", e.what());
		// Usar valores padrão em caso de erro
		Keys::CurrentAimbotKey = Keys::GetFKeyFromKeyCode(1); // LeftMouseButton
		Keys::CurrentBulletTPKey = Keys::GetFKeyFromKeyCode(2); // RightMouseButton
	} catch (...) {
		printf("Unknown exception when initializing keys\n");
		// Usar valores padrão em caso de erro
		Keys::CurrentAimbotKey = Keys::GetFKeyFromKeyCode(1); // LeftMouseButton
		Keys::CurrentBulletTPKey = Keys::GetFKeyFromKeyCode(2); // RightMouseButton
	}

	// Verificar o estado das teclas, mesmo quando o menu não está aberto
	KeyCaptureSystem::CheckForKeyPress();

    // Obter os ponteiros mais atualizados a cada frame para evitar race conditions.
    // A falha anterior ocorreu por não atualizar o ponteiro 'Engine' junto com o 'World'.

    // Verificação robusta antes de chamar DrawTransition
	if (IsValidObjectPtr(Variables::World) && IsValidObjectPtr(Variables::Engine))
	{
		// Verificar DrawLists do ImGui - log apenas falhas
		ImDrawList* BackgroundList = nullptr;
		ImDrawList* ForegroundList = nullptr;

		try
		{
			BackgroundList = ImGui::GetBackgroundDrawList();
			ForegroundList = ImGui::GetForegroundDrawList();

			if (IsValidPtr(BackgroundList) && IsValidPtr(ForegroundList))
			{
				DrawTransition(Variables::World, Variables::Engine, BackgroundList, ForegroundList);
				SafetySystem::RegisterSuccess(); // DrawTransition executado com sucesso
			}
			else
			{
				SafetySystem::LogError("MainLoop", "ImGui DrawLists inválidas");
			}
		}
		catch (...)
		{
			SafetySystem::LogError("MainLoop", "EXCEÇÃO ao obter ImGui DrawLists");
		}
	}
	else
	{
		SafetySystem::LogError("MainLoop", "World ou Engine inválidos - pulando DrawTransition");
	}

	// Inicializar animação da UI
	AnimatedWidgets::Get().BeginUIAnimation(ShowMenu);

	if (AnimatedWidgets::Get().ShouldRenderUI())
	{
		// Aplicar animação de entrada/saída
		float animationProgress = AnimatedWidgets::Get().GetUIAnimationProgress();

		// Definir tamanho e posição da janela com valores fixos mais razoáveis
		ImVec2 displaySize = ImGui::GetIO().DisplaySize;
		// Usar um tamanho fixo de 800x600 para a janela
		ImGui::SetNextWindowSize(ImVec2(800, 600), ImGuiCond_FirstUseEver);
		// Centralizar a janela na tela
		ImGui::SetNextWindowPos(ImVec2(displaySize.x * 0.5f - 400, displaySize.y * 0.5f - 300), ImGuiCond_FirstUseEver);

		// Aplicar efeito de fade e escala baseado na animação
		ImGui::PushStyleVar(ImGuiStyleVar_Alpha, animationProgress);

		// Efeito de escala suave na entrada
		float scale = 0.8f + (0.2f * animationProgress); // Escala de 0.8 a 1.0
		ImGui::GetIO().FontGlobalScale = mods::uiZoomFactor * scale;

		if (ImGui::Begin("Jocasta Protocol", nullptr, ImGuiWindowFlags_NoCollapse))
		{

			// Criação das abas com animação
			AnimatedWidgets::Get().BeginTabBarAnimated("MainTabBar");
			{
				// ABA 1: AIMBOT E BULLET TP
				if (AnimatedWidgets::Get().BeginTabItemAnimated(LanguageSystem::Get().GetText(TextID::TabAimbotBulletTP)))
				{
					ImGui::Spacing();

					// Seção Aimbot
					GoldBoldTextWithShadow(LanguageSystem::Get().GetText(TextID::AimbotSettings));
					ImGui::Separator();
					ImGui::Spacing();

					AnimatedWidgets::Get().AnimatedCheckbox(LanguageSystem::Get().GetText(TextID::AimbotLabel), &mods::aimbot);
					AddTooltipLocalized(TextID::Aimbot);

					// Configurações básicas do aimbot (sempre visíveis)
					AnimatedWidgets::Get().AnimatedCheckbox(LanguageSystem::Get().GetText(TextID::ShowAimbotFOVCircleLabel), &mods::aimbotFovCircle);
					AddTooltipLocalized(TextID::ShowAimbotFOVCircle);
					ImGui::SliderInt(LanguageSystem::Get().GetText(TextID::AimbotFOVRadiusLabel), &mods::fov, 1, 50);
					AddTooltipLocalized(TextID::AimbotFOVRadius);

					AnimatedWidgets::Get().AnimatedCheckbox(LanguageSystem::Get().GetText(TextID::FocusLowestHealthLabel), &mods::focusLowestHealth);
					AddTooltipLocalized(TextID::FocusLowestHealth);

					if (mods::aimbot)
					{
						ImGui::Spacing();
						ImGui::Separator();
						ImGui::Spacing();

						//---------------------------------------------------------------------
						// 		🎯	Smoothing Configuration Block
						//---------------------------------------------------------------------
						GoldBoldTextWithShadow(LanguageSystem::Get().GetText(TextID::SmoothingSettings));
						ImGui::Separator();

						// Opção para controle separado de Pitch e Yaw
						AnimatedWidgets::Get().AnimatedCheckbox(LanguageSystem::Get().GetText(TextID::SeparatePitchYawSmoothingLabel), &mods::separatePitchYawSmoothing);
						AddTooltipLocalized(TextID::SeparatePitchYawSmoothing);

						if (mods::separatePitchYawSmoothing)
						{
							// Controles separados para Pitch e Yaw
							ImGui::SliderFloat(LanguageSystem::Get().GetText(TextID::SmoothingPitchLabel), &mods::smoothAmountPitch, 0.0f, 10.0f, "%.1f");
							AddTooltipLocalized(TextID::SmoothingPitch);

							ImGui::SliderFloat(LanguageSystem::Get().GetText(TextID::SmoothingYawLabel), &mods::smoothAmountYaw, 0.0f, 10.0f, "%.1f");
							AddTooltipLocalized(TextID::SmoothingYaw);
						}
						else
						{
							// Controle único para ambos
							ImGui::SliderFloat(LanguageSystem::Get().GetText(TextID::SmoothingLabel), &mods::smoothAmount, 0.0f, 10.0f, "%.1f");
							AddTooltipLocalized(TextID::Smoothing);
						}

						//---------------------------------------------------------------------
						// 		⚙️	Advanced Smoothing Configuration Block
						//---------------------------------------------------------------------
						if (ImGui::TreeNode(LanguageSystem::Get().GetText(TextID::AdvancedAimbotSettingsLabel)))
						{
							//---------------------------------------------------------------------
							// 		🎯	Adaptive Smoothing Settings
							//---------------------------------------------------------------------
							GoldBoldTextWithShadow(LanguageSystem::Get().GetText(TextID::AdaptiveMovementSettings));
							ImGui::Separator();

							AnimatedWidgets::Get().AnimatedCheckbox(LanguageSystem::Get().GetText(TextID::UseAdaptiveMovementLabel), &mods::useAdaptiveMovement);
							AddTooltipLocalized(TextID::UseAdaptiveMovement);

							if (mods::useAdaptiveMovement)
							{
								ImGui::SliderFloat(LanguageSystem::Get().GetText(TextID::AdaptiveDurationLabel), &mods::adaptiveDuration, 0.0f, 10.0f, "%.1f");
								AddTooltipLocalized(TextID::AdaptiveDuration);

								ImGui::Spacing();
								ImGui::Separator();
								ImGui::TextColored(*ThemeColors::GOLD_TEXT, "Adaptive Smoothing");

								// Usar o valor do smoothAmount como valor inicial e máximo para o smoothing adaptativo
								if (!mods::separatePitchYawSmoothing)
								{
									// Atualizar o valor inicial para ser igual ao smoothAmount
									mods::initialSmoothAmount = mods::smoothAmount;

									// Garantir que o valor mínimo não exceda o valor inicial
									if (mods::minSmoothAmount > mods::initialSmoothAmount)
										mods::minSmoothAmount = mods::initialSmoothAmount;

									// Slider para o valor mínimo
									ImGui::SliderFloat(LanguageSystem::Get().GetText(TextID::MinimumSmoothingLabel), &mods::minSmoothAmount, 0.0f, mods::initialSmoothAmount, "%.1f");
									AddTooltipLocalized(TextID::MinimumSmoothing);

									// Mostrar informação sobre o valor inicial
									ImGui::TextColored(*ThemeColors::GREEN_INFO, "Initial value: %.1f (from Smoothing)", mods::initialSmoothAmount);
								}

								AnimatedWidgets::Get().AnimatedCheckbox(LanguageSystem::Get().GetText(TextID::SeparateAdaptiveSettingsLabel), &mods::separateAdaptiveSettings);
								AddTooltipLocalized(TextID::SeparateAdaptiveSettings);

								if (mods::separateAdaptiveSettings)
								{
									// Atualizar os valores iniciais para serem iguais aos valores de smoothing
									mods::initialSmoothAmountPitch = mods::smoothAmountPitch;
									mods::initialSmoothAmountYaw = mods::smoothAmountYaw;

									// Garantir que os valores mínimos não excedam os valores iniciais
									if (mods::minSmoothAmountPitch > mods::initialSmoothAmountPitch)
										mods::minSmoothAmountPitch = mods::initialSmoothAmountPitch;

									if (mods::minSmoothAmountYaw > mods::initialSmoothAmountYaw)
										mods::minSmoothAmountYaw = mods::initialSmoothAmountYaw;

									// Sliders para os valores mínimos
									ImGui::SliderFloat(LanguageSystem::Get().GetText(TextID::MinimumPitchSmoothingLabel), &mods::minSmoothAmountPitch, 0.0f, mods::initialSmoothAmountPitch, "%.1f");
									AddTooltipLocalized(TextID::MinimumPitchSmoothing);
									ImGui::TextColored(*ThemeColors::GREEN_INFO, "Initial value: %.1f (from Pitch Smoothing)", mods::initialSmoothAmountPitch);

									ImGui::SliderFloat(LanguageSystem::Get().GetText(TextID::MinimumYawSmoothingLabel), &mods::minSmoothAmountYaw, 0.0f, mods::initialSmoothAmountYaw, "%.1f");
									AddTooltipLocalized(TextID::MinimumYawSmoothing);
									ImGui::TextColored(*ThemeColors::GREEN_INFO, "Initial value: %.1f (from Yaw Smoothing)", mods::initialSmoothAmountYaw);
								}
							}

							//---------------------------------------------------------------------
							// 		🎯	Inertia Settings
							//---------------------------------------------------------------------
							ImGui::Spacing();
							GoldBoldTextWithShadow(LanguageSystem::Get().GetText(TextID::InertiaSettings));
							ImGui::Separator();

							AnimatedWidgets::Get().AnimatedCheckbox(LanguageSystem::Get().GetText(TextID::UseInertiaLabel), &mods::useInertia);
							AddTooltipLocalized(TextID::UseInertia);

							if (mods::useInertia)
							{
								ImGui::SliderFloat(LanguageSystem::Get().GetText(TextID::InertiaFactorLabel), &mods::inertiaFactor, 0.0f, 10.0f, "%.1f");
								AddTooltipLocalized(TextID::InertiaFactor);

								// Configurações de inércia adaptativa
								ImGui::Spacing();
								ImGui::Separator();

								AnimatedWidgets::Get().AnimatedCheckbox(LanguageSystem::Get().GetText(TextID::UseAdaptiveInertiaLabel), &mods::useAdaptiveInertia);
								AddTooltipLocalized(TextID::UseAdaptiveInertia);

								if (mods::useAdaptiveInertia)
								{
									// Atualizar o valor inicial para ser igual ao inertiaFactor
									mods::initialInertiaFactor = mods::inertiaFactor;

									// Garantir que o valor mínimo não exceda o valor inicial
									if (mods::minInertiaFactor > mods::initialInertiaFactor)
										mods::minInertiaFactor = mods::initialInertiaFactor;

									// Slider para o valor mínimo
									ImGui::SliderFloat(LanguageSystem::Get().GetText(TextID::MinimumInertiaFactorLabel), &mods::minInertiaFactor, 0.0f, mods::initialInertiaFactor, "%.1f");
									AddTooltipLocalized(TextID::MinimumInertiaFactor);

									// Mostrar informação sobre o valor inicial
									ImGui::TextColored(*ThemeColors::GREEN_INFO, "Initial value: %.1f (from Inertia Factor)", mods::initialInertiaFactor);
								}
							}

							//---------------------------------------------------------------------
							// 		⏱️	Timing Settings
							//---------------------------------------------------------------------
							ImGui::Spacing();
							GoldBoldTextWithShadow(LanguageSystem::Get().GetText(TextID::DelaySettingsLabel));
							ImGui::Separator();

							AnimatedWidgets::Get().AnimatedCheckbox(LanguageSystem::Get().GetText(TextID::UseAimDelayLabel), &mods::useAimDelay);
							AddTooltipLocalized(TextID::UseAimDelay);

							if (mods::useAimDelay)
							{
								ImGui::SliderFloat(LanguageSystem::Get().GetText(TextID::AimDelayTimeLabel), &mods::aimDelayTime, 0.0f, 1.0f, "%.2f");
								AddTooltipLocalized(TextID::AimDelayTime);
							}

							//---------------------------------------------------------------------
							// 		⚡	Flick System Settings
							//---------------------------------------------------------------------
							ImGui::Spacing();
							GoldBoldTextWithShadow(LanguageSystem::Get().GetText(TextID::FlickSettingsLabel));
							ImGui::Separator();

							AnimatedWidgets::Get().AnimatedCheckbox(LanguageSystem::Get().GetText(TextID::UseFlickLabel), &mods::useFlick);
							AddTooltipLocalized(TextID::UseFlick);

							if (mods::useFlick)
							{
								ImGui::SliderFloat(LanguageSystem::Get().GetText(TextID::FlickDurationLabel), &mods::flickDuration, 0.1f, 3.0f, "%.1f");
								AddTooltipLocalized(TextID::FlickDuration);

								// Combo box para tipo de redução
								const char* reductionTypes[] = { "Linear", "Exponential", "Quadratic" };
								ImGui::Combo(LanguageSystem::Get().GetText(TextID::FlickReductionTypeLabel), &mods::flickReductionType, reductionTypes, IM_ARRAYSIZE(reductionTypes));
								AddTooltipLocalized(TextID::FlickReductionType);
							}

							//---------------------------------------------------------------------
							// 		🎭	Humanization & Targeting Settings
							//---------------------------------------------------------------------
							ImGui::Spacing();
							GoldBoldTextWithShadow(LanguageSystem::Get().GetText(TextID::HumanizationSettings));
							ImGui::Separator();

							// Seleção da parte do corpo
							const char* bodyPartOptions[] = {
								LanguageSystem::Get().GetText(TextID::HeadOption),
								LanguageSystem::Get().GetText(TextID::NeckOption),
								LanguageSystem::Get().GetText(TextID::ChestOption),
								LanguageSystem::Get().GetText(TextID::SpineOption),
								"Pelvis",
								LanguageSystem::Get().GetText(TextID::RandomOption),
								LanguageSystem::Get().GetText(TextID::HumanizedOption)
							};

							ImGui::Combo(LanguageSystem::Get().GetText(TextID::TargetBodyPartModeLabel), &mods::targetBodyPartMode, bodyPartOptions, IM_ARRAYSIZE(bodyPartOptions));
							AddTooltipLocalized(TextID::TargetBodyPartMode);

							// Se o modo humanizado estiver selecionado, mostrar configurações adicionais
							if (mods::targetBodyPartMode == 6) // 6 = Humanizado
							{
								// Checkbox para ativar/desativar sistema de humanização
								AnimatedWidgets::Get().AnimatedCheckbox("Enable Humanized Targeting", &mods::useHumanizedTargeting);
								if (ImGui::IsItemHovered())
								{
									ImGui::SetTooltip("Enable or disable the humanized targeting system");
								}

								if (mods::useHumanizedTargeting)
								{
									// Configurações de probabilidade (baseado no projeto de exemplo)
									ImGui::Spacing();
									ImGui::TextColored(*ThemeColors::GOLD_TEXT, "Body Part Probabilities (0.0-1.0):");

									ImGui::SliderFloat(LanguageSystem::Get().GetText(TextID::HeadProbabilityLabel), &mods::headProbability, 0.0f, 1.0f, "%.2f");
									AddTooltipLocalized(TextID::HeadProbability);

									ImGui::SliderFloat(LanguageSystem::Get().GetText(TextID::NeckProbabilityLabel), &mods::neckProbability, 0.0f, 1.0f, "%.2f");
									AddTooltipLocalized(TextID::NeckProbability);

									ImGui::SliderFloat(LanguageSystem::Get().GetText(TextID::ChestProbabilityLabel), &mods::chestProbability, 0.0f, 1.0f, "%.2f");
									AddTooltipLocalized(TextID::ChestProbability);

									ImGui::SliderFloat(LanguageSystem::Get().GetText(TextID::SpineProbabilityLabel), &mods::spineProbability, 0.0f, 1.0f, "%.2f");
									AddTooltipLocalized(TextID::SpineProbability);

									ImGui::SliderFloat("Pelvis Probability", &mods::pelvisProbability, 0.0f, 1.0f, "%.2f");
									if (ImGui::IsItemHovered())
									{
										ImGui::SetTooltip("Probability of targeting the pelvis when using humanized mode");
									}

									// Informação sobre as probabilidades
									float totalProb = mods::headProbability + mods::neckProbability + mods::chestProbability + mods::spineProbability + mods::pelvisProbability;
									if (totalProb <= 0.0f)
									{
										ImGui::TextColored(*ThemeColors::ORANGE_WARNING, "Info: All probabilities are zero. Equal distribution will be used.");
									}
									else
									{
										ImGui::TextColored(*ThemeColors::GREEN_INFO, "Info: Probabilities will be normalized (Total: %.2f)", totalProb);
									}

									// Configurações de intervalo dinâmico (baseado no projeto de exemplo)
									ImGui::Spacing();
									ImGui::TextColored(*ThemeColors::GOLD_TEXT, "Dynamic Timing (seconds):");

									ImGui::SliderFloat("Min Change Time", &mods::bodyPartChangeMinTime, 0.05f, 1.0f, "%.2f");
									if (ImGui::IsItemHovered())
									{
										ImGui::SetTooltip("Minimum time between body part changes in humanized mode");
									}

									ImGui::SliderFloat("Max Change Time", &mods::bodyPartChangeMaxTime, mods::bodyPartChangeMinTime, 2.0f, "%.2f");
									if (ImGui::IsItemHovered())
									{
										ImGui::SetTooltip("Maximum time between body part changes in humanized mode");
									}

									// Garantir que o tempo máximo seja sempre maior que o mínimo
									if (mods::bodyPartChangeMaxTime < mods::bodyPartChangeMinTime)
									{
										mods::bodyPartChangeMaxTime = mods::bodyPartChangeMinTime;
									}
								}
							}
							// Se o modo aleatório estiver selecionado, mostrar configuração de intervalo
							else if (mods::targetBodyPartMode == 5) // 5 = Aleatório
							{
								ImGui::SliderFloat(LanguageSystem::Get().GetText(TextID::BodyPartUpdateIntervalLabel), &mods::bodyPartUpdateInterval, 0.5f, 10.0f, "%.1f");
								AddTooltipLocalized(TextID::BodyPartUpdateInterval);

								ImGui::TextColored(*ThemeColors::GREEN_INFO, "Note: Random mode uses variable intervals (2-4 seconds)");
							}

							ImGui::TreePop();
						}
					}

					ImGui::Spacing();
					ImGui::Separator();
					ImGui::Spacing();

					//---------------------------------------------------------------------
					// 		🤝	Team & Support Settings
					//---------------------------------------------------------------------
					GoldBoldTextWithShadow(LanguageSystem::Get().GetText(TextID::SupportSettingsLabel));
					ImGui::Separator();
					ImGui::Spacing();

					// Team Target Mode (alternar entre mirar em inimigos ou aliados)
					bool previousTeamTargetMode = mods::teamTargetMode;
					AnimatedWidgets::Get().AnimatedCheckbox(LanguageSystem::Get().GetText(TextID::TeamTargetModeLabel), &mods::teamTargetMode);
					AddTooltipLocalized(TextID::TeamTargetModeTooltip);

					// 🛡️ PROTEÇÃO: Limpar projéteis rastreados quando Team Target Mode muda
					if (previousTeamTargetMode != mods::teamTargetMode)
					{
						Variables::TrackedProjectiles.clear();
						Variables::CurrentBulletTPTarget = 0;
						Variables::TotalProjectilesFired = 0;
					}

					// Visão de Suporte (ignorar FOV para aimbot)
					bool previousSupportVision = mods::isSupportVisionEnabled;
					AnimatedWidgets::Get().AnimatedCheckbox(LanguageSystem::Get().GetText(TextID::SupportVisionLabel), &mods::isSupportVisionEnabled);
					AddTooltipLocalized(TextID::SupportVisionTooltip);

					// 🛡️ PROTEÇÃO: Limpar projéteis rastreados quando Support Vision é desativado
					if (previousSupportVision && !mods::isSupportVisionEnabled)
					{
						Variables::TrackedProjectiles.clear();
						Variables::CurrentBulletTPTarget = 0;
						Variables::TotalProjectilesFired = 0;
					}

					// First Enemy Lock (trava no primeiro inimigo)
					AnimatedWidgets::Get().AnimatedCheckbox(LanguageSystem::Get().GetText(TextID::FirstEnemyLockLabel), &mods::firstEnemyLock);
					AddTooltipLocalized(TextID::FirstEnemyLockTooltip);

					// Target Invisible Enemies (mirar em inimigos invisíveis)
					AnimatedWidgets::Get().AnimatedCheckbox(LanguageSystem::Get().GetText(TextID::TargetInvisibleEnemiesLabel), &mods::targetInvisibleEnemies);
					AddTooltipLocalized(TextID::TargetInvisibleEnemiesTooltip);

					// Priorizar alvos voadores
					AnimatedWidgets::Get().AnimatedCheckbox(LanguageSystem::Get().GetText(TextID::FlyingTargetPriorityLabel), &mods::shouldPrioritizeFlyingTargets);
					AddTooltipLocalized(TextID::FlyingTargetPriorityTooltip);

					if (mods::shouldPrioritizeFlyingTargets)
					{
						ImGui::SliderFloat(LanguageSystem::Get().GetText(TextID::FlyingVelocityThresholdLabel), &mods::flyingVelocityThreshold, 10.0f, 200.0f, "%.1f");
						AddTooltipLocalized(TextID::FlyingVelocityThresholdTooltip);
					}

					// TEMPORARIAMENTE REMOVIDO: Priorizar summons base
					// AnimatedWidgets::Get().AnimatedCheckbox("Prioritize Summons", &mods::shouldPrioritizeSummons);
					// if (ImGui::IsItemHovered())
					// {
					//     ImGui::SetTooltip("When enabled, aimbot will prioritize targeting summons (like pets, minions) over players");
					// }

					// TEMPORARIAMENTE REMOVIDO: Priorizar construtos de habilidade
					// AnimatedWidgets::Get().AnimatedCheckbox("Prioritize Constructs", &mods::shouldPrioritizeConstructs);
					// if (ImGui::IsItemHovered())
					// {
					//     ImGui::SetTooltip("When enabled, aimbot will prioritize targeting real constructs:\n- Namor's squids (ASummoned_10455101)\n- Rocket's beacon (ASummoned_10234101)\n- Loki's rune stones & ability areas (AMarvelAbilityTargetActor_Scope)\n- Magneto's barriers (AMarvelBarrierBase)");
					// }

					ImGui::Spacing();
					ImGui::Spacing();

					//---------------------------------------------------------------------
					// 		🎯	Bullet Teleport Settings
					//---------------------------------------------------------------------
					GoldBoldTextWithShadow(LanguageSystem::Get().GetText(TextID::BulletTPSettings));
					ImGui::Separator();
					ImGui::Spacing();

					AnimatedWidgets::Get().AnimatedCheckbox(LanguageSystem::Get().GetText(TextID::BulletTPLabel), &mods::bullet_tp);
					AddTooltipLocalized(TextID::BulletTP);

					// Configurações básicas do Bullet TP
					AnimatedWidgets::Get().AnimatedCheckbox(LanguageSystem::Get().GetText(TextID::ShowBulletTPFOVCircleLabel), &mods::bullet_tp_fov_circle);
					AddTooltipLocalized(TextID::ShowBulletTPFOVCircle);
					ImGui::SliderInt(LanguageSystem::Get().GetText(TextID::BulletTPFOVRadiusLabel), &mods::bullet_tp_fov, 1, 10);
					AddTooltipLocalized(TextID::BulletTPFOVRadius);

					// Seleção do socket de alvo (FUNCIONAL)
					const char *sockets[] = {"Head", "Neck", "Chest (spine_02)", "Spine (spine_01)", "Pelvis", "Random"};
					ImGui::Combo(LanguageSystem::Get().GetText(TextID::BulletTPTargetLabel), &mods::bullet_tp_target, sockets, IM_ARRAYSIZE(sockets));
					if (ImGui::IsItemHovered())
					{
						ImGui::SetTooltip("Target socket for bullet redirection:\n"
										  "• Head: Smallest target, highest precision\n"
										  "• Neck: Small target, good for headshots\n"
										  "• Chest: Larger target, better collision detection (RECOMMENDED)\n"
										  "• Spine: Center mass, reliable hit registration\n"
										  "• Pelvis: Lower body, good for body shots\n"
										  "• Random: Randomly selects from available sockets\n"
										  "\nNOTE: Now includes automatic hit registration bypass for all sockets!");
					}

					// Opções avançadas para o BulletTP
					if (ImGui::TreeNode(LanguageSystem::Get().GetText(TextID::AdvancedBulletTPSettingsLabel)))
					{
						GoldBoldTextWithShadow(LanguageSystem::Get().GetText(TextID::AdvancedBulletTPSettings));

						ImGui::Spacing();
						ImGui::Separator();
						ImGui::Spacing();

						// Configurações de comportamento
						AnimatedWidgets::Get().AnimatedCheckbox(LanguageSystem::Get().GetText(TextID::ContinueAfterKeyReleaseLabel), &mods::bulletTPContinueAfterKeyUp);
						AddTooltipLocalized(TextID::ContinueAfterKeyRelease);

						if (mods::bulletTPContinueAfterKeyUp)
						{
							ImGui::SliderFloat(LanguageSystem::Get().GetText(TextID::ContinueDurationLabel), &mods::bulletTPKeyUpDuration, 0.0f, 10.0f, "%.1f");
							AddTooltipLocalized(TextID::ContinueDuration);
						}

						ImGui::Spacing();
						ImGui::Separator();
						ImGui::Spacing();

						// Configurações de depuração
						AnimatedWidgets::Get().AnimatedCheckbox(LanguageSystem::Get().GetText(TextID::DebugModeLabel), &mods::bulletTPDebug);
						AddTooltipLocalized(TextID::DebugMode);

						ImGui::TreePop();
					}

					ImGui::EndTabItem();
				}

				// ABA 2: KEYBINDS E CHECKS
				if (AnimatedWidgets::Get().BeginTabItemAnimated(LanguageSystem::Get().GetText(TextID::KeybindsAndChecksTab)))
				{
					ImGui::Spacing();

					//---------------------------------------------------------------------
					// 		⌨️	Keybind Configuration
					//---------------------------------------------------------------------
					GoldBoldTextWithShadow(LanguageSystem::Get().GetText(TextID::KeybindSettings));
					ImGui::Separator();
					ImGui::Spacing();

					// Obter os nomes das teclas atuais
					std::string aimbotKeyName = "None";
					std::string bulletTPKeyName = "None";
					std::string teamTargetKeyName = "None";
					std::string gamepadAimbotButtonName = "None";
					std::string gamepadBulletTPButtonName = "None";
					std::string gamepadTeamTargetButtonName = "None";

					// Obter o nome da tecla do aimbot
					if (mods::aimbot_key > 0) {
						aimbotKeyName = Keys::GetKeyNameFromCustomCode(mods::aimbot_key);
					}

					// Obter o nome da tecla do bullet TP
					if (mods::bullet_tp_key > 0) {
						bulletTPKeyName = Keys::GetKeyNameFromCustomCode(mods::bullet_tp_key);
					}

					// Obter o nome da tecla do Team Target Mode
					if (mods::teamTargetKey > 0) {
						teamTargetKeyName = Keys::GetKeyNameFromCustomCode(mods::teamTargetKey);
					}

					// Obter o nome do botão do gamepad para aimbot
					if (mods::gamepad_aimbot_button >= 0 && mods::gamepad_aimbot_button < 14) {
						gamepadAimbotButtonName = Keys::GamepadButtonNames[mods::gamepad_aimbot_button];
					}

					// Obter o nome do botão do gamepad para bullet TP
					if (mods::gamepad_bullet_tp_button >= 0 && mods::gamepad_bullet_tp_button < 14) {
						gamepadBulletTPButtonName = Keys::GamepadButtonNames[mods::gamepad_bullet_tp_button];
					}

					// Obter o nome do botão do gamepad para Team Target Mode
					if (mods::gamepadTeamTargetButton >= 0 && mods::gamepadTeamTargetButton < 14) {
						gamepadTeamTargetButtonName = Keys::GamepadButtonNames[mods::gamepadTeamTargetButton];
					}

					// Desenhar os botões de captura de tecla
					KeyCaptureSystem::DrawKeyCaptureButtonPair(
						LanguageSystem::Get().GetText(TextID::AimbotKeyLabel),
						aimbotKeyName.c_str(), KeyCaptureSystem::KeyCaptureType::AimbotKey,
						gamepadAimbotButtonName.c_str(), KeyCaptureSystem::KeyCaptureType::GamepadAimbotButton,
						LanguageSystem::Get().GetText(TextID::AimbotKey)
					);

					KeyCaptureSystem::DrawKeyCaptureButtonPair(
						LanguageSystem::Get().GetText(TextID::BulletTPKeyLabel),
						bulletTPKeyName.c_str(), KeyCaptureSystem::KeyCaptureType::BulletTPKey,
						gamepadBulletTPButtonName.c_str(), KeyCaptureSystem::KeyCaptureType::GamepadBulletTPButton,
						LanguageSystem::Get().GetText(TextID::BulletTPKey)
					);

					KeyCaptureSystem::DrawKeyCaptureButtonPair(
						LanguageSystem::Get().GetText(TextID::TeamTargetKeyLabel),
						teamTargetKeyName.c_str(), KeyCaptureSystem::KeyCaptureType::TeamTargetKey,
						gamepadTeamTargetButtonName.c_str(), KeyCaptureSystem::KeyCaptureType::GamepadTeamTargetButton,
						LanguageSystem::Get().GetText(TextID::TeamTargetKeyTooltip)
					);

					// Verificar se há uma captura de tecla em andamento
					KeyCaptureSystem::CheckForKeyPress();

					ImGui::Spacing();
					ImGui::Spacing();

					//---------------------------------------------------------------------
					// 		✅	Validation & Targeting Checks
					//---------------------------------------------------------------------
					GoldBoldTextWithShadow(LanguageSystem::Get().GetText(TextID::CheckSettings));
					ImGui::Separator();
					ImGui::Spacing();

					// Basic validation checks
					AnimatedWidgets::Get().AnimatedCheckbox(LanguageSystem::Get().GetText(TextID::TeamCheckLabel), &mods::TeamCheck);
					AddTooltipLocalized(TextID::TeamCheck);
					AnimatedWidgets::Get().AnimatedCheckbox(LanguageSystem::Get().GetText(TextID::SelfCheckLabel), &mods::LocalCheck);
					AddTooltipLocalized(TextID::SelfCheck);
					AnimatedWidgets::Get().AnimatedCheckbox(LanguageSystem::Get().GetText(TextID::VisibleCheckLabel), &mods::VisCheck);
					AddTooltipLocalized(TextID::VisibleCheck);

					// Advanced visibility options
					if (mods::VisCheck)
					{
						ImGui::Indent();
						AnimatedWidgets::Get().AnimatedCheckbox(LanguageSystem::Get().GetText(TextID::UseLineOfSightLabel), &mods::UseLineOfSight);
						AddTooltipLocalized(TextID::UseLineOfSight);
						ImGui::Unindent();
					}

					ImGui::Spacing();
					ImGui::Separator();
					ImGui::Spacing();

					//---------------------------------------------------------------------
					// 		🎯	Role-Based Targeting
					//---------------------------------------------------------------------
					GoldBoldTextWithShadow(LanguageSystem::Get().GetText(TextID::RoleTargetSettings));
					ImGui::Separator();

					AnimatedWidgets::Get().AnimatedCheckbox(LanguageSystem::Get().GetText(TextID::EnableRoleTargetLabel), &mods::enableRoleTarget);
					AddTooltipLocalized(TextID::EnableRoleTargetTooltip);

					if (mods::enableRoleTarget)
					{
						const char *roles[] = {
							LanguageSystem::Get().GetText(TextID::AllRolesOption),
							LanguageSystem::Get().GetText(TextID::TankOption),
							LanguageSystem::Get().GetText(TextID::DamageOption),
							LanguageSystem::Get().GetText(TextID::SupportOption)
						};
						ImGui::Combo(LanguageSystem::Get().GetText(TextID::TargetRoleLabel), &mods::targetRole, roles, IM_ARRAYSIZE(roles));
						AddTooltipLocalized(TextID::TargetRoleTooltip);
					}

					ImGui::EndTabItem();
				}

				// ABA 3: VISUALS & INTERFACE
				if (AnimatedWidgets::Get().BeginTabItemAnimated(LanguageSystem::Get().GetText(TextID::TabVisuals)))
				{
					ImGui::Spacing();

					//---------------------------------------------------------------------
					// 		👁️	ESP & Visual Information
					//---------------------------------------------------------------------
					GoldBoldTextWithShadow(LanguageSystem::Get().GetText(TextID::ESPSettings));
					ImGui::Separator();
					ImGui::Spacing();

					// Checkbox principal para ativar/desativar o ESP
					AnimatedWidgets::Get().AnimatedCheckbox(LanguageSystem::Get().GetText(TextID::EnableESPLabel), &mods::esp);
					AddTooltipLocalized(TextID::EnableESP);

					// Opções individuais do ESP (só mostradas se o ESP estiver ativado)
					if (mods::esp)
					{
						ImGui::Indent();

						// 📦 Seção de Boxes
						if (ImGui::CollapsingHeader(LanguageSystem::Get().GetText(TextID::BoxesHeaderLabel)))
						{
							AnimatedWidgets::Get().AnimatedCheckbox(LanguageSystem::Get().GetText(TextID::ShowBoxesLabel), &mods::showBoxes);
							if (mods::showBoxes)
							{
								ImGui::Indent();
								const char* boxTypes[] = {
									LanguageSystem::Get().GetText(TextID::BoxType2DLabel),
									LanguageSystem::Get().GetText(TextID::BoxTypeCorneredLabel),
									LanguageSystem::Get().GetText(TextID::BoxTypeFilledLabel)
								};
								ImGui::Combo(LanguageSystem::Get().GetText(TextID::BoxTypeLabel), &mods::boxType, boxTypes, IM_ARRAYSIZE(boxTypes));
								AnimatedWidgets::Get().AnimatedCheckbox(LanguageSystem::Get().GetText(TextID::BoxOutlineLabel), &mods::boxOutline);
								ImGui::SliderFloat(LanguageSystem::Get().GetText(TextID::ThicknessLabel), &mods::boxThickness, 0.5f, 5.0f);
								ImGui::Unindent();
							}
						}

						// 💚 Seção de Barras de Saúde
						if (ImGui::CollapsingHeader(LanguageSystem::Get().GetText(TextID::HealthBarsHeaderLabel)))
						{
							AnimatedWidgets::Get().AnimatedCheckbox(LanguageSystem::Get().GetText(TextID::ShowHealthBarLabel), &mods::showHealthBar);
							if (mods::showHealthBar)
							{
								ImGui::Indent();
								const char* positions[] = {
									LanguageSystem::Get().GetText(TextID::PositionLeftLabel),
									LanguageSystem::Get().GetText(TextID::PositionRightLabel),
									LanguageSystem::Get().GetText(TextID::PositionTopLabel),
									LanguageSystem::Get().GetText(TextID::PositionBottomLabel)
								};
								ImGui::Combo(LanguageSystem::Get().GetText(TextID::PositionLabel), &mods::healthBarPosition, positions, IM_ARRAYSIZE(positions));
								ImGui::Unindent();
							}
							AnimatedWidgets::Get().AnimatedCheckbox(LanguageSystem::Get().GetText(TextID::ShowHealthLabel), &mods::ShowHealth);
							AddTooltipLocalized(TextID::ShowHealth);
						}

						// 🦴 Seção de Skeleton
						if (ImGui::CollapsingHeader(LanguageSystem::Get().GetText(TextID::SkeletonESPHeaderLabel)))
						{
							AnimatedWidgets::Get().AnimatedCheckbox(LanguageSystem::Get().GetText(TextID::ShowSkeletonLabel), &mods::showSkeleton);
							if (mods::showSkeleton)
							{
								ImGui::Indent();
								ImGui::SliderFloat(LanguageSystem::Get().GetText(TextID::ThicknessLabel), &mods::skeletonThickness, 0.5f, 3.0f);
								ImGui::Unindent();
							}
						}

						// 📝 Seção de Informações
						if (ImGui::CollapsingHeader(LanguageSystem::Get().GetText(TextID::InformationHeaderLabel)))
						{
							AnimatedWidgets::Get().AnimatedCheckbox(LanguageSystem::Get().GetText(TextID::ShowPlayerNamesLabel), &mods::showPlayerNames);
							AnimatedWidgets::Get().AnimatedCheckbox(LanguageSystem::Get().GetText(TextID::ShowDistanceLabel), &mods::ShowDistance);
							AddTooltipLocalized(TextID::ShowDistance);
							AnimatedWidgets::Get().AnimatedCheckbox(LanguageSystem::Get().GetText(TextID::ShowUltimatePercentageLabel), &mods::showUltimatePercentage);
							AddTooltipLocalized(TextID::ShowUltimatePercentage);
						}

						// 📏 Seção de Tracers
						if (ImGui::CollapsingHeader(LanguageSystem::Get().GetText(TextID::TracersLinesHeaderLabel)))
						{
							AnimatedWidgets::Get().AnimatedCheckbox(LanguageSystem::Get().GetText(TextID::TracerLinesLabel), &mods::TracerLines);
							AddTooltipLocalized(TextID::TracerLines);
							AnimatedWidgets::Get().AnimatedCheckbox(LanguageSystem::Get().GetText(TextID::SnapLinesLabel), &mods::showSnapLines);
						}

						// 🎯 Seção de Crosshair
						if (ImGui::CollapsingHeader(LanguageSystem::Get().GetText(TextID::CrosshairHeaderLabel)))
						{
							AnimatedWidgets::Get().AnimatedCheckbox(LanguageSystem::Get().GetText(TextID::ShowCrosshairLabel), &mods::showCrosshair);
							if (mods::showCrosshair)
							{
								ImGui::Indent();
								const char* crosshairTypes[] = {
									LanguageSystem::Get().GetText(TextID::CrosshairDotLabel),
									LanguageSystem::Get().GetText(TextID::CrosshairCrossLabel),
									LanguageSystem::Get().GetText(TextID::CrosshairCircleLabel)
								};
								ImGui::Combo(LanguageSystem::Get().GetText(TextID::TypeLabel), &mods::crosshairType, crosshairTypes, IM_ARRAYSIZE(crosshairTypes));
								ImGui::SliderFloat(LanguageSystem::Get().GetText(TextID::SizeLabel), &mods::crosshairSize, 1.0f, 20.0f);
								ImGui::SliderFloat(LanguageSystem::Get().GetText(TextID::ThicknessLabel), &mods::crosshairThickness, 0.5f, 5.0f);
								ImGui::ColorEdit3(LanguageSystem::Get().GetText(TextID::ColorLabel), (float*)&mods::crosshairColor);
								ImGui::Unindent();
							}
						}

						// ⚙️ Seção de Configurações Avançadas
						if (ImGui::CollapsingHeader(LanguageSystem::Get().GetText(TextID::AdvancedConfigHeaderLabel)))
						{
							ImGui::SliderFloat(LanguageSystem::Get().GetText(TextID::MaxDistanceLabel), &mods::maxESPDistance, 100.0f, 1000.0f);
							AnimatedWidgets::Get().AnimatedCheckbox(LanguageSystem::Get().GetText(TextID::ShowSummonsESPLabel), &mods::showSummonsESP);
						}

						ImGui::Unindent();
					}

					// Opção de Glow
					AnimatedWidgets::Get().AnimatedCheckbox(LanguageSystem::Get().GetText(TextID::EnableGlowLabel), &mods::enableGlow);
					AddTooltipLocalized(TextID::EnableGlow);

					ImGui::Spacing();
					ImGui::Separator();
					ImGui::Spacing();

					//---------------------------------------------------------------------
					// 		🔍	Field of View Settings
					//---------------------------------------------------------------------
					GoldBoldTextWithShadow(LanguageSystem::Get().GetText(TextID::FOVSettings));
					ImGui::Separator();
					ImGui::Spacing();

					AnimatedWidgets::Get().AnimatedCheckbox(LanguageSystem::Get().GetText(TextID::FOVChangerLabel), &mods::fov_changer);
					AddTooltipLocalized(TextID::FOVChanger);
					ImGui::SliderInt(LanguageSystem::Get().GetText(TextID::FOVSliderLabel), &mods::fov_changer_amount, 1, 360);
					AddTooltipLocalized(TextID::FOVSlider);

					ImGui::Spacing();
					ImGui::Separator();
					ImGui::Spacing();

					//---------------------------------------------------------------------
					// 		🖥️	Interface Settings
					//---------------------------------------------------------------------
					GoldBoldTextWithShadow(LanguageSystem::Get().GetText(TextID::UIZoomSettings));
					ImGui::Separator();
					ImGui::Spacing();

					AnimatedWidgets::Get().AnimatedCheckbox(LanguageSystem::Get().GetText(TextID::EnableUIZoomLabel), &mods::enableUIZoom);
					AddTooltip(LanguageSystem::Get().GetText(TextID::EnableUIZoom));

					ImGui::SliderFloat(LanguageSystem::Get().GetText(TextID::UIZoomFactorLabel), &mods::uiZoomFactor, mods::uiZoomMin, mods::uiZoomMax, "%.1f");
					AddTooltip(LanguageSystem::Get().GetText(TextID::UIZoomFactor));

					if (ImGui::Button(LanguageSystem::Get().GetText(TextID::ResetZoomButton)))
					{
						mods::uiZoomFactor = 1.0f; // Resetar para o valor padrão
					}

					ImGui::EndTabItem();
				}

				// ABA 4: CONFIGURAÇÕES
				if (AnimatedWidgets::Get().BeginTabItemAnimated(LanguageSystem::Get().GetText(TextID::TabConfig)))
				{
					ImGui::Spacing();

					//---------------------------------------------------------------------
					// 		🌐	Language & Interface Settings
					//---------------------------------------------------------------------
					GoldBoldTextWithShadow(LanguageSystem::Get().GetText(TextID::ConfigSettings));
					ImGui::Separator();
					ImGui::Spacing();

					// Seletor de idioma
					const char *languages[] = {"English", "Português", "Español", "Français", "Русский", "Italiano"};
					ImGui::Text("%s", LanguageSystem::Get().GetText(TextID::LanguageLabel));
					ImGui::SameLine();
					if (ImGui::Combo("##Language", &mods::currentLanguage, languages, IM_ARRAYSIZE(languages)))
					{
						// Atualizar o idioma no sistema
						LanguageSystem::Get().SetLanguage(static_cast<Language>(mods::currentLanguage));
					}
					AddTooltipLocalized(TextID::Language);

					ImGui::Spacing();

					// Seletor de tema
					const char *themes[] = {"Dark Blue & Gold", "Orange & Gold"};
					ImGui::Text("%s", LanguageSystem::Get().GetText(TextID::ThemeLabel));
					ImGui::SameLine();
					if (ImGui::Combo("##Theme", &mods::currentTheme, themes, IM_ARRAYSIZE(themes)))
					{
						// Aplicar o tema selecionado
						ApplyTheme(static_cast<UITheme>(mods::currentTheme));
					}

					ImGui::Spacing();
					ImGui::Separator();
					ImGui::Spacing();

					//---------------------------------------------------------------------
					// 		💾	Profile Management
					//---------------------------------------------------------------------
					GoldBoldTextWithShadow(LanguageSystem::Get().GetText(TextID::ConfigSettings));
					ImGui::Separator();
					ImGui::Spacing();

					// Mostrar a configuração atual
					ImGui::TextColored(*ThemeColors::YELLOW_HIGHLIGHT, "%s %s", LanguageSystem::Get().GetText(TextID::CurrentConfigLabel), currentConfigFile);
					ImGui::Spacing();

					// Botão para salvar a configuração atual
					static char saveConfigName[128] = "";
					ImGui::InputText(LanguageSystem::Get().GetText(TextID::ConfigNameLabel), saveConfigName, IM_ARRAYSIZE(saveConfigName));
					ImGui::SameLine();
					if (ImGui::Button(LanguageSystem::Get().GetText(TextID::SaveButton)))
					{
						if (strlen(saveConfigName) > 0)
						{
							char filename[256];
							sprintf_s(filename, "%s.cfg", saveConfigName);
							if (SaveConfig(filename))
							{
								strcpy_s(currentConfigFile, filename);
								memset(saveConfigName, 0, sizeof(saveConfigName));
							}
						}
					}

					ImGui::Spacing();

					// Botão para atualizar o perfil atual
					if (ImGui::Button(LanguageSystem::Get().GetText(TextID::UpdateCurrentProfileButton)))
					{
						if (SaveConfig(currentConfigFile))
						{
							// Perfil atualizado com sucesso
							// Opcional: mostrar uma mensagem de confirmação
						}
					}
					if (ImGui::IsItemHovered())
					{
						ImGui::BeginTooltip();
						ImGui::Text("%s: %s", LanguageSystem::Get().GetText(TextID::UpdateCurrentProfileTooltip), currentConfigFile);
						ImGui::EndTooltip();
					}

					ImGui::Spacing();
					ImGui::Separator();
					ImGui::Spacing();

					// Listar configurações disponíveis
					ImGui::TextUnformatted(LanguageSystem::Get().GetText(TextID::AvailableConfigurationsLabel));
					ImGui::Spacing();

					// Obter a lista de arquivos de configuração
					static std::vector<std::string> configFiles;
					static float lastRefreshTime = 0.0f;
					float currentTime = ImGui::GetTime();

					// Atualizar a lista a cada 2 segundos ou quando o botão de atualização for pressionado
					static bool refreshList = true;
					if (currentTime - lastRefreshTime > 2.0f || refreshList)
					{
						configFiles = ListConfigFiles();
						lastRefreshTime = currentTime;
						refreshList = false;
					}

					// Botão para atualizar a lista
					if (ImGui::Button(LanguageSystem::Get().GetText(TextID::RefreshListButton)))
					{
						refreshList = true;
					}

					ImGui::Spacing();

					// Mostrar a lista de configurações
					for (const auto &configFile : configFiles)
					{
						// Destacar a configuração atual
						bool isCurrentConfig = (configFile == currentConfigFile);
						if (isCurrentConfig)
							ImGui::PushStyleColor(ImGuiCol_Text, *ThemeColors::CYAN_CURRENT);

						// Botão para carregar a configuração
						std::string loadButtonText = std::string(LanguageSystem::Get().GetText(TextID::LoadButton)) + " ##" + configFile;
						if (ImGui::Button(loadButtonText.c_str()))
						{
							if (LoadConfig(configFile.c_str()))
							{
								// Atualizar as teclas após carregar a configuração
								Keys::CurrentAimbotKey = Keys::GetFKeyFromKeyCode(mods::aimbot_key);
								Keys::CurrentBulletTPKey = Keys::GetFKeyFromKeyCode(mods::bullet_tp_key);

								// Atualizar o idioma
								LanguageSystem::Get().SetLanguage(static_cast<Language>(mods::currentLanguage));
							}
						}

						ImGui::SameLine();

						// Botão para excluir a configuração
						std::string deleteButtonText = std::string(LanguageSystem::Get().GetText(TextID::DeleteButton)) + " ##" + configFile;
						if (ImGui::Button(deleteButtonText.c_str()))
						{
							char fullPath[512];
							sprintf_s(fullPath, "%s/%s", CONFIG_DIR, configFile.c_str());
							if (remove(fullPath) == 0)
							{
								refreshList = true;
								if (configFile == currentConfigFile)
								{
									// Se o perfil excluído for o atual, carregar o perfil padrão
									strcpy_s(currentConfigFile, DEFAULT_CONFIG);
									LoadConfig(DEFAULT_CONFIG);
								}
							}
						}

						ImGui::SameLine();
						ImGui::Text("%s", configFile.c_str());

						if (isCurrentConfig)
							ImGui::PopStyleColor();
					}

					ImGui::EndTabItem();
				}

				AnimatedWidgets::Get().EndTabBarAnimated();
			}

			ImGui::End();
		}

		// Restaurar o estilo após a animação
		ImGui::PopStyleVar(); // Alpha

		// Restaurar escala da fonte
		ImGui::GetIO().FontGlobalScale = mods::uiZoomFactor;
	}

	ImGui::EndFrame();

	// Verificação robusta do DirectX - log apenas falhas críticas
	try
	{
		if (!IsValidPtr(DirectX12Interface::FrameContext))
		{
			SafetySystem::LogError("DirectXRender", "FrameContext é null");
			return oPresent(pSwapChain, SyncInterval, Flags);
		}

		UINT currentBackBufferIndex = pSwapChain->GetCurrentBackBufferIndex();

		if (currentBackBufferIndex >= DirectX12Interface::BuffersCounts)
		{
			SafetySystem::LogError("DirectXRender", ("BackBufferIndex inválido: " + std::to_string(currentBackBufferIndex)).c_str());
			return oPresent(pSwapChain, SyncInterval, Flags);
		}

		DirectX12Interface::_FrameContext &CurrentFrameContext = DirectX12Interface::FrameContext[currentBackBufferIndex];

		if (!IsValidPtr(CurrentFrameContext.CommandAllocator))
		{
			SafetySystem::LogError("DirectXRender", "CommandAllocator é null");
			return oPresent(pSwapChain, SyncInterval, Flags);
		}

		CurrentFrameContext.CommandAllocator->Reset();

	D3D12_RESOURCE_BARRIER Barrier;
	Barrier.Type = D3D12_RESOURCE_BARRIER_TYPE_TRANSITION;
	Barrier.Flags = D3D12_RESOURCE_BARRIER_FLAG_NONE;
	Barrier.Transition.pResource = CurrentFrameContext.Resource;
	Barrier.Transition.Subresource = D3D12_RESOURCE_BARRIER_ALL_SUBRESOURCES;
	Barrier.Transition.StateBefore = D3D12_RESOURCE_STATE_PRESENT;
	Barrier.Transition.StateAfter = D3D12_RESOURCE_STATE_RENDER_TARGET;

		// Verificar CommandList antes de usar
		if (!IsValidPtr(DirectX12Interface::CommandList))
		{
			SafetySystem::LogError("DirectXRender", "CommandList é null");
			return oPresent(pSwapChain, SyncInterval, Flags);
		}

		DirectX12Interface::CommandList->Reset(CurrentFrameContext.CommandAllocator, nullptr);
		DirectX12Interface::CommandList->ResourceBarrier(1, &Barrier);
		DirectX12Interface::CommandList->OMSetRenderTargets(1, &CurrentFrameContext.DescriptorHandle, FALSE, nullptr);

		if (!IsValidPtr(DirectX12Interface::DescriptorHeapImGuiRender))
		{
			SafetySystem::LogError("DirectXRender", "DescriptorHeapImGuiRender é null");
			return oPresent(pSwapChain, SyncInterval, Flags);
		}

		DirectX12Interface::CommandList->SetDescriptorHeaps(1, &DirectX12Interface::DescriptorHeapImGuiRender);
		ImGui::Render();
		ImGui_ImplDX12_RenderDrawData(ImGui::GetDrawData(), DirectX12Interface::CommandList);

		Barrier.Transition.StateBefore = D3D12_RESOURCE_STATE_RENDER_TARGET;
		Barrier.Transition.StateAfter = D3D12_RESOURCE_STATE_PRESENT;
		DirectX12Interface::CommandList->ResourceBarrier(1, &Barrier);
		DirectX12Interface::CommandList->Close();

		// Verificar CommandQueue antes de executar
		if (!IsValidPtr(DirectX12Interface::CommandQueue))
		{
			SafetySystem::LogError("DirectXRender", "CommandQueue é null");
			return oPresent(pSwapChain, SyncInterval, Flags);
		}

		DirectX12Interface::CommandQueue->ExecuteCommandLists(1, reinterpret_cast<ID3D12CommandList *const *>(&DirectX12Interface::CommandList));
		SafetySystem::RegisterSuccess(); // DirectX renderizou com sucesso
	}
	catch (...)
	{
		SafetySystem::LogError("DirectXRender", "EXCEÇÃO durante renderização DirectX");
		return oPresent(pSwapChain, SyncInterval, Flags);
	}

	//---------------------------------------------------------------------
	// 		🔄	Label para Sistema de Retry - Ponto de Saída Antecipada
	//---------------------------------------------------------------------
skip_world_processing:
	return oPresent(pSwapChain, SyncInterval, Flags);
}

//=========================================================================================================================//

void hkExecuteCommandLists(ID3D12CommandQueue *queue, UINT NumCommandLists, ID3D12CommandList *ppCommandLists)
{
	if (!DirectX12Interface::CommandQueue)
		DirectX12Interface::CommandQueue = queue;

	oExecuteCommandLists(queue, NumCommandLists, ppCommandLists);
}

//=========================================================================================================================//

void APIENTRY hkDrawInstanced(ID3D12GraphicsCommandList *dCommandList, UINT VertexCountPerInstance, UINT InstanceCount, UINT StartVertexLocation, UINT StartInstanceLocation)
{

	return oDrawInstanced(dCommandList, VertexCountPerInstance, InstanceCount, StartVertexLocation, StartInstanceLocation);
}

//=========================================================================================================================//

void APIENTRY hkDrawIndexedInstanced(ID3D12GraphicsCommandList *dCommandList, UINT IndexCountPerInstance, UINT InstanceCount, UINT StartIndexLocation, INT BaseVertexLocation, UINT StartInstanceLocation)
{
	return oDrawIndexedInstanced(dCommandList, IndexCountPerInstance, InstanceCount, StartIndexLocation, BaseVertexLocation, StartInstanceLocation);
}

//=========================================================================================================================//

DWORD WINAPI MainThread(LPVOID lpParameter)
{
	bool WindowFocus = false;
	while (WindowFocus == false)
	{
		DWORD ForegroundWindowProcessID;
		GetWindowThreadProcessId(GetForegroundWindow(), &ForegroundWindowProcessID);
		if (GetCurrentProcessId() == ForegroundWindowProcessID)
		{

			Process::ID = GetCurrentProcessId();
			Process::Handle = GetCurrentProcess();
			Process::Hwnd = GetForegroundWindow();

			RECT TempRect;
			GetWindowRect(Process::Hwnd, &TempRect);
			Process::WindowWidth = TempRect.right - TempRect.left;
			Process::WindowHeight = TempRect.bottom - TempRect.top;

			char TempTitle[MAX_PATH];
			GetWindowText(Process::Hwnd, TempTitle, sizeof(TempTitle));
			Process::Title = TempTitle;

			char TempClassName[MAX_PATH];
			GetClassName(Process::Hwnd, TempClassName, sizeof(TempClassName));
			Process::ClassName = TempClassName;

			char TempPath[MAX_PATH];
			GetModuleFileNameEx(Process::Handle, NULL, TempPath, sizeof(TempPath));
			Process::Path = TempPath;

			WindowFocus = true;
		}
	}
	bool InitHook = false;
	while (!InitHook)
	{
		if (DirectX12::Init() == true)
		{
			CreateHook(54, (void **)&oExecuteCommandLists, hkExecuteCommandLists);
			CreateHook(140, (void **)&oPresent, hkPresent);
			CreateHook(84, (void **)&oDrawInstanced, hkDrawInstanced);
			CreateHook(85, (void **)&oDrawIndexedInstanced, hkDrawIndexedInstanced);
			InitHook = true;
		}
		else
		{
			Sleep(100); // Aguarda 100ms antes de tentar novamente
		}
	}
	return 0;
}

//=========================================================================================================================//

BOOL APIENTRY DllMain(HMODULE hModule, DWORD dwReason, LPVOID lpReserved)
{
	switch (dwReason)
	{
	case DLL_PROCESS_ATTACH:
		DisableThreadLibraryCalls(hModule);
		Process::Module = hModule;
		CoInitializeEx(NULL, COINIT_MULTITHREADED); // Inicializa COM para a thread principal
		CreateThread(0, 0, MainThread, 0, 0, 0);
		break;
	case DLL_PROCESS_DETACH:
		CoUninitialize(); // Libera COM ao descarregar a DLL
		FreeLibraryAndExitThread(hModule, TRUE);
		DisableAll();
		break;
	}
	return TRUE;
}

#ifdef _MSC_VER
#pragma warning(pop) // Restaurar avisos no final do arquivo
#endif