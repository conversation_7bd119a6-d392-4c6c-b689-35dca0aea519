#ifndef LANGUAGE_SYSTEM_H
#define LANGUAGE_SYSTEM_H

#include <string>
#include <unordered_map>

// Enumeração para os idiomas suportados
enum class Language
{
    English,
    Portuguese,
    Spanish,
    French,
    Russian,
    Italian
};

// Enumeração para as strings de texto
enum class TextID
{
    // Tooltips - Aimbot
    Aimbot,
    Smoothing,
    SeparatePitchYawSmoothing,
    SmoothingPitch,
    SmoothingYaw,
    FocusLowestHealth,
    ShowAimbotFOVCircle,
    AimbotFOVRadius,

    // Tooltips - Aimbot Advanced
    UseInertia,
    InertiaFactor,
    UseAimDelay,
    AimDelayTime,
    UseAdaptiveMovement,
    AdaptiveDuration,
    InitialSmoothing,
    MinimumSmoothing,
    SeparateAdaptiveSettings,
    InitialPitchSmoothing,
    MinimumPitchSmoothing,
    InitialYawSmoothing,
    MinimumYawSmoothing,
    UseAdaptiveInertia,
    InitialInertiaFactor,
    MinimumInertiaFactor,

    // Tooltips - Flick System
    UseFlick,
    FlickDuration,
    FlickReductionType,

    // Tooltips - Bullet TP
    BulletTP,
    ShowBulletTPFOVCircle,
    BulletTPFOVRadius,
    BulletTPTarget,
    MinSafeDistance,
    MaxSafeDistance,
    ContinueAfterKeyRelease,
    ContinueDuration,
    DebugMode,

    // Tooltips - Humanization
    TargetBodyPartMode,
    HeadProbability,
    NeckProbability,
    ChestProbability,
    SpineProbability,
    BodyPartUpdateInterval,

    // Tooltips - Keybinds
    UseGamepad,
    AimbotKey,
    BulletTPKey,

    // Tooltips - Checks
    SelfCheck,
    VisibleCheck,
    UseLineOfSight,
    TeamCheck,

    // Tooltips - ESP
    EnableESP,
    TracerLines,
    ShowHealth,
    ShowDistance,
    ShowUltimatePercentage,
    EnableGlow,

    // Tooltips - FOV
    FOVChanger,
    FOVSlider,

    // Tooltips - Config
    Language,
    SaveConfig,
    LoadConfig,
    DeleteConfig,

    // Section Headers
    AimbotSettings,
    SmoothingSettings,
    BulletTPSettings,
    AdvancedAimbotSettings,
    InertiaSettings,
    AdaptiveMovementSettings,
    HumanizationSettings,

    KeybindSettings,
    CheckSettings,
    ESPSettings,
    FOVSettings,
    ConfigSettings,
    AdvancedBulletTPSettings,
    AvailableConfigurations,
    CurrentConfig,

    // Menu Tabs
    TabAimbotBulletTP,
    TabVisuals,
    TabMisc,
    TabConfig,

    // Menu Items
    AimbotLabel,
    SmoothingLabel,
    SeparatePitchYawSmoothingLabel,
    SmoothingPitchLabel,
    SmoothingYawLabel,
    FocusLowestHealthLabel,
    ShowAimbotFOVCircleLabel,
    AimbotFOVRadiusLabel,

    // Advanced Aimbot Labels
    AdvancedAimbotSettingsLabel,
    UseInertiaLabel,
    InertiaFactorLabel,
    UseAimDelayLabel,
    AimDelayTimeLabel,
    UseAdaptiveMovementLabel,
    AdaptiveDurationLabel,
    InitialSmoothingLabel,
    MinimumSmoothingLabel,
    SeparateAdaptiveSettingsLabel,
    InitialPitchSmoothingLabel,
    MinimumPitchSmoothingLabel,
    InitialYawSmoothingLabel,
    MinimumYawSmoothingLabel,
    UseAdaptiveInertiaLabel,
    InitialInertiaFactorLabel,
    MinimumInertiaFactorLabel,

    // Flick System Labels
    FlickSettingsLabel,
    UseFlickLabel,
    FlickDurationLabel,
    FlickReductionTypeLabel,

    // Humanization Labels
    HumanizationSettingsLabel,
    TargetBodyPartModeLabel,
    ChestOption,
    SpineOption,
    HumanizedOption,
    HeadProbabilityLabel,
    NeckProbabilityLabel,
    ChestProbabilityLabel,
    SpineProbabilityLabel,
    BodyPartUpdateIntervalLabel,

    // Bullet TP Labels
    BulletTPLabel,
    ShowBulletTPFOVCircleLabel,
    BulletTPFOVRadiusLabel,
    BulletTPTargetLabel,
    HeadOption,
    NeckOption,
    RandomOption,
    AdvancedBulletTPSettingsLabel,
    MinSafeDistanceLabel,
    MaxSafeDistanceLabel,
    ContinueAfterKeyReleaseLabel,
    ContinueDurationLabel,
    DebugModeLabel,

    UseGamepadLabel,
    AimbotKeyLabel,
    BulletTPKeyLabel,
    SelfCheckLabel,
    VisibleCheckLabel,
    UseLineOfSightLabel,
    TeamCheckLabel,
    EnableESPLabel,
    TracerLinesLabel,
    ShowHealthLabel,
    ShowDistanceLabel,
    ShowUltimatePercentageLabel,
    EnableGlowLabel,

    // ESP Moderno Labels
    ShowSummonsESPLabel,
    SummonLabel,
    CloneLabel,
    ConstructLabel,

    // ESP Interface Headers
    BoxesHeaderLabel,
    HealthBarsHeaderLabel,
    SkeletonESPHeaderLabel,
    InformationHeaderLabel,
    TracersLinesHeaderLabel,
    CrosshairHeaderLabel,
    AdvancedConfigHeaderLabel,

    // ESP Checkbox Labels
    ShowBoxesLabel,
    ShowHealthBarLabel,
    ShowSkeletonLabel,
    ShowPlayerNamesLabel,
    ShowCrosshairLabel,
    BoxOutlineLabel,

    // ESP Configuration Labels
    BoxTypeLabel,
    ThicknessLabel,
    PositionLabel,
    TypeLabel,
    SizeLabel,
    ColorLabel,
    MaxDistanceLabel,

    // ESP Box Type Options
    BoxType2DLabel,
    BoxTypeCorneredLabel,
    BoxTypeFilledLabel,

    // ESP Health Bar Position Options
    PositionLeftLabel,
    PositionRightLabel,
    PositionTopLabel,
    PositionBottomLabel,

    // ESP Crosshair Type Options
    CrosshairDotLabel,
    CrosshairCrossLabel,
    CrosshairCircleLabel,

    // ESP Additional Labels
    SnapLinesLabel,
    FOVChangerLabel,
    FOVSliderLabel,
    LanguageLabel,
    ThemeLabel,
    SaveConfigLabel,
    LoadConfigLabel,
    DeleteConfigLabel,
    ConfigNameLabel,
    SaveButtonLabel,
    LoadButtonLabel,
    DeleteButtonLabel,

    // Role Target
    RoleTargetSettings,
    EnableRoleTargetLabel,
    TargetRoleLabel,
    AllRolesOption,
    TankOption,
    DamageOption,
    SupportOption,

    // Tabs
    KeybindsAndChecksTab,

    // App Title
    AppTitle,

    // Config UI
    CurrentConfigLabel,
    RefreshListButton,
    AvailableConfigurationsLabel,
    SaveButton,
    LoadButton,
    DeleteButton,

    // Advanced Settings
    DelaySettingsLabel,

    // Role Target Tooltips
    EnableRoleTargetTooltip,
    TargetRoleTooltip,

    // Support Settings
    SupportSettingsLabel,

    // Team Target Mode
    TeamTargetModeLabel,
    TeamTargetModeTooltip,
    TeamTargetKeyLabel,
    TeamTargetKeyTooltip,

    // Support Vision
    SupportVisionLabel,
    SupportVisionTooltip,

    // First Enemy Lock
    FirstEnemyLockLabel,
    FirstEnemyLockTooltip,

    // Target Invisible Enemies
    TargetInvisibleEnemiesLabel,
    TargetInvisibleEnemiesTooltip,

    // Flying Target Priority
    FlyingTargetPriorityLabel,
    FlyingTargetPriorityTooltip,
    FlyingVelocityThresholdLabel,
    FlyingVelocityThresholdTooltip,

    // UI Zoom Settings
    UIZoomSettings,
    EnableUIZoom,
    EnableUIZoomLabel,
    UIZoomFactor,
    UIZoomFactorLabel,
    ResetZoomButton,

    // Humanization System
    EnableHumanizedTargetingLabel,
    EnableHumanizedTargetingTooltip,
    BodyPartProbabilitiesLabel,
    ProbabilitiesZeroInfoLabel,
    ProbabilitiesNormalizedInfoLabel,
    DynamicTimingLabel,
    MinChangeTimeLabel,
    MinChangeTimeTooltip,
    MaxChangeTimeLabel,
    MaxChangeTimeTooltip,
    RandomModeInfoLabel,
    InitialValueFromInertiaLabel,
    InitialValueFromSmoothingLabel,
    InitialValueFromPitchSmoothingLabel,
    InitialValueFromYawSmoothingLabel,
    AdaptiveSmoothingLabel,
    NoneKeyLabel,

    // Profile Management
    UpdateCurrentProfileButton,
    UpdateCurrentProfileTooltip
};

// Classe para gerenciar o sistema de idiomas
class LanguageSystem
{
public:
    // Singleton
    static LanguageSystem &Get()
    {
        static LanguageSystem instance;
        return instance;
    }

    // Inicializar o sistema de idiomas
    void Initialize();

    // Obter uma string de texto no idioma atual
    const char *GetText(TextID id) const;

    // Definir o idioma atual
    void SetLanguage(Language lang);

    // Obter o idioma atual
    Language GetCurrentLanguage() const { return m_currentLanguage; }

    // Obter o nome do idioma atual
    const char *GetCurrentLanguageName() const;

    // Obter a lista de idiomas disponíveis
    const std::unordered_map<Language, const char *> &GetAvailableLanguages() const { return m_languageNames; }

private:
    LanguageSystem() : m_currentLanguage(Language::English) {}
    ~LanguageSystem() = default;

    // Idioma atual
    Language m_currentLanguage;

    // Mapa de strings para cada idioma
    std::unordered_map<Language, std::unordered_map<TextID, std::string>> m_strings;

    // Nomes dos idiomas
    std::unordered_map<Language, const char *> m_languageNames;
};

#endif // LANGUAGE_SYSTEM_H
