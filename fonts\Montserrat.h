#ifndef MONTSERRAT_H
#define MONTSERRAT_H

//---------------------------------------------------------------------
// 		🔤	Montserrat Regular Font Header
//---------------------------------------------------------------------
// Este arquivo contém os dados da fonte Montserrat-Regular extraída
// e as funções para carregamento no ImGui

#ifdef __cplusplus
extern "C" {
#endif

// Array com os bytes da fonte Montserrat-Regular (definido em Montserrat-Regular.c)
extern unsigned char rawData[330948];

#ifdef __cplusplus
}
#endif

// Dados da fonte Montserrat-Regular para uso com ImGui
static const unsigned char montserrat_regular_data[] = {
#include "../fonts/Montserrat-Regular.c"
};

// Tamanho do array da fonte
static const int montserrat_regular_size = sizeof(montserrat_regular_data);

//---------------------------------------------------------------------
// 		⚙️	Função de Carregamento da Fonte
//---------------------------------------------------------------------
// Carrega a fonte Montserrat-Regular no ImGui
bool LoadMontserratFont();

#endif // MONTSERRAT_H
