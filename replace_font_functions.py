#!/usr/bin/env python3
# -*- coding: utf-8 -*-

def replace_font_functions():
    try:
        # Ler o arquivo main.cpp
        with open('main.cpp', 'r', encoding='utf-8') as f:
            content = f.read()

        # Fazer a substituição
        new_content = content.replace('GoldTextWithShadow(', 'GoldBoldTextWithShadow(')
        
        # Salvar o arquivo
        with open('main.cpp', 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        # Contar quantas substituições foram feitas
        count = content.count('GoldTextWithShadow(')
        print(f'Substituição concluída: {count} ocorrências de GoldTextWithShadow -> GoldBoldTextWithShadow')
        
    except Exception as e:
        print(f'Erro: {e}')

if __name__ == '__main__':
    replace_font_functions()
