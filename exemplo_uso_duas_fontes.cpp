//---------------------------------------------------------------------
// 		📝	Exemplo de Uso das Duas Fontes Montserrat
//---------------------------------------------------------------------

#include "montserrat_font.h"
#include "montserrat_bold_font.h"
#include "ImGui/imgui.h"

//---------------------------------------------------------------------
// 		🎨	Exemplo de Interface com Hierarquia Visual
//---------------------------------------------------------------------

void ExemploInterfaceCompleta()
{
    if (ImGui::Begin("Exemplo de Fontes Montserrat"))
    {
        // TÍTULO PRINCIPAL - Fonte Bold com sombra dourada
        GoldBoldTextWithShadow("CONFIGURAÇÕES PRINCIPAIS");
        ImGui::Separator();
        ImGui::Spacing();
        
        // Texto normal - Fonte Regular (automática)
        ImGui::Text("Esta interface demonstra o uso das duas fontes Montserrat:");
        ImGui::BulletText("Montserrat Regular (16px) - Para texto normal");
        ImGui::BulletText("Montserrat Bold (18px) - Para títulos e destaques");
        
        ImGui::Spacing();
        
        // SEÇÃO 1 - Título Bold
        GoldBoldTextWithShadow("Seção de Configurações");
        ImGui::Separator();
        
        // Conteúdo normal
        ImGui::Text("Aqui temos texto normal em Montserrat Regular.");
        
        // Subtítulo em Bold simples
        BoldText("Subtítulo em Bold:");
        ImGui::Text("• Opção 1 em texto normal");
        ImGui::Text("• Opção 2 em texto normal");
        
        // Título dourado sem sombra
        GoldBoldText("Título Dourado em Bold");
        ImGui::Text("Texto explicativo em fonte regular.");
        
        ImGui::Spacing();
        
        // SEÇÃO 2 - Outro título principal
        GoldBoldTextWithShadow("Demonstração de Controles");
        ImGui::Separator();
        
        // Controles com texto normal
        static bool checkbox = false;
        ImGui::Checkbox("Checkbox em fonte regular", &checkbox);
        
        static float slider = 0.5f;
        ImGui::SliderFloat("Slider em fonte regular", &slider, 0.0f, 1.0f);
        
        // Botão com texto normal
        if (ImGui::Button("Botão em fonte regular"))
        {
            // Ação do botão
        }
        
        ImGui::Spacing();
        
        // SEÇÃO 3 - Comparação direta
        GoldBoldTextWithShadow("Comparação de Fontes");
        ImGui::Separator();
        
        ImGui::Text("Texto em Montserrat Regular (fonte padrão)");
        BoldText("Texto em Montserrat Bold (função auxiliar)");
        
        // Uso direto das fontes
        ImGuiIO& io = ImGui::GetIO();
        
        if (io.Fonts->Fonts.Size > 1)
        {
            ImGui::PushFont(io.Fonts->Fonts[0]); // Regular
            ImGui::Text("Acesso direto: Montserrat Regular");
            ImGui::PopFont();
            
            ImGui::PushFont(io.Fonts->Fonts[1]); // Bold
            ImGui::Text("Acesso direto: Montserrat Bold");
            ImGui::PopFont();
        }
    }
    ImGui::End();
}

//---------------------------------------------------------------------
// 		🎯	Exemplo de Menu de Configurações Real
//---------------------------------------------------------------------

void ExemploMenuConfiguracoes()
{
    if (ImGui::Begin("Configurações do Jogo"))
    {
        if (ImGui::BeginTabBar("ConfigTabs"))
        {
            if (ImGui::BeginTabItem("Gráficos"))
            {
                // Título da seção - Bold com sombra
                GoldBoldTextWithShadow("Configurações de Vídeo");
                ImGui::Separator();
                ImGui::Spacing();
                
                // Subtítulo - Bold simples
                BoldText("Resolução e Qualidade:");
                
                // Controles normais
                static int resolution = 0;
                const char* resolutions[] = {"1920x1080", "2560x1440", "3840x2160"};
                ImGui::Combo("Resolução", &resolution, resolutions, 3);
                
                static int quality = 2;
                const char* qualities[] = {"Baixa", "Média", "Alta", "Ultra"};
                ImGui::Combo("Qualidade", &quality, qualities, 4);
                
                ImGui::Spacing();
                
                // Outra seção
                GoldBoldTextWithShadow("Configurações Avançadas");
                ImGui::Separator();
                
                static bool vsync = true;
                ImGui::Checkbox("V-Sync", &vsync);
                
                static bool fullscreen = false;
                ImGui::Checkbox("Tela Cheia", &fullscreen);
                
                ImGui::EndTabItem();
            }
            
            if (ImGui::BeginTabItem("Áudio"))
            {
                GoldBoldTextWithShadow("Configurações de Áudio");
                ImGui::Separator();
                ImGui::Spacing();
                
                BoldText("Volume Principal:");
                static float masterVolume = 0.8f;
                ImGui::SliderFloat("Master", &masterVolume, 0.0f, 1.0f);
                
                BoldText("Volumes Específicos:");
                static float musicVolume = 0.6f;
                static float sfxVolume = 0.9f;
                ImGui::SliderFloat("Música", &musicVolume, 0.0f, 1.0f);
                ImGui::SliderFloat("Efeitos", &sfxVolume, 0.0f, 1.0f);
                
                ImGui::EndTabItem();
            }
            
            ImGui::EndTabBar();
        }
    }
    ImGui::End();
}

//---------------------------------------------------------------------
// 		🔧	Exemplo de Painel de Debug
//---------------------------------------------------------------------

void ExemploPainelDebug()
{
    if (ImGui::Begin("Debug Info"))
    {
        GoldBoldTextWithShadow("Informações do Sistema");
        ImGui::Separator();
        
        ImGui::Text("FPS: 60");
        ImGui::Text("Frame Time: 16.67ms");
        ImGui::Text("Memory: 512MB");
        
        ImGui::Spacing();
        
        GoldBoldTextWithShadow("Status das Fontes");
        ImGui::Separator();
        
        ImGuiIO& io = ImGui::GetIO();
        ImGui::Text("Fontes carregadas: %d", io.Fonts->Fonts.Size);
        
        if (io.Fonts->Fonts.Size > 0)
        {
            BoldText("Fonte 0 (Regular):");
            ImGui::Text("  Tamanho: %.1fpx", io.Fonts->Fonts[0]->FontSize);
            ImGui::Text("  Status: %s", io.Fonts->Fonts[0] ? "OK" : "ERRO");
        }
        
        if (io.Fonts->Fonts.Size > 1)
        {
            BoldText("Fonte 1 (Bold):");
            ImGui::Text("  Tamanho: %.1fpx", io.Fonts->Fonts[1]->FontSize);
            ImGui::Text("  Status: %s", io.Fonts->Fonts[1] ? "OK" : "ERRO");
        }
        
        ImGui::Spacing();
        
        GoldBoldTextWithShadow("Teste de Renderização");
        ImGui::Separator();
        
        ImGui::Text("Texto normal em Montserrat Regular");
        BoldText("Texto em Montserrat Bold");
        GoldBoldText("Texto dourado em Bold");
        GoldBoldTextWithShadow("Texto dourado com sombra em Bold");
    }
    ImGui::End();
}

//---------------------------------------------------------------------
// 		📋	Função Principal de Demonstração
//---------------------------------------------------------------------

void DemonstrarFontes()
{
    // Chamar as diferentes demonstrações
    ExemploInterfaceCompleta();
    ExemploMenuConfiguracoes();
    ExemploPainelDebug();
}

//---------------------------------------------------------------------
// 		💡	Dicas de Uso
//---------------------------------------------------------------------

/*
DICAS PARA USO DAS FONTES:

1. HIERARQUIA VISUAL:
   - GoldBoldTextWithShadow() → Títulos principais de seções
   - BoldText() → Subtítulos e destaques
   - ImGui::Text() → Texto normal (usa Montserrat Regular automaticamente)

2. CORES E ESTILOS:
   - GoldBoldTextWithShadow() → Dourado com sombra preta
   - GoldBoldText() → Dourado sem sombra
   - BoldText() → Cor normal do tema

3. TAMANHOS:
   - Montserrat Regular: 16px (padrão)
   - Montserrat Bold: 18px (títulos)
   - Fonte do sistema: 18px (fallback)

4. ACESSO DIRETO:
   - io.Fonts->Fonts[0] → Montserrat Regular
   - io.Fonts->Fonts[1] → Montserrat Bold
   - io.Fonts->Fonts[2] → Fonte do sistema

5. VERIFICAÇÃO DE SEGURANÇA:
   - Sempre verificar se io.Fonts->Fonts.Size > índice
   - Sempre verificar se a fonte não é nullptr
   - As funções auxiliares já fazem essas verificações

6. PERFORMANCE:
   - Evitar PushFont/PopFont desnecessários
   - Usar as funções auxiliares quando possível
   - Agrupar elementos da mesma fonte
*/
